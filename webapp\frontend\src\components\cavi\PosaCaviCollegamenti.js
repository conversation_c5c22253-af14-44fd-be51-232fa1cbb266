import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  CircularProgress,
  FormHelperText,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormLabel
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Cable as CableIcon,
  Save as SaveIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import caviService from '../../services/caviService';
import { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';
import { redirectToVisualizzaCavi } from '../../utils/navigationUtils';
import CavoForm from './CavoForm';
import SelezionaCavoForm from './SelezionaCavoForm';

const PosaCaviCollegamenti = ({ cantiereId: propCantiereId, onSuccess, onError, initialOption = null, preselectedCavo = null, dialogOnly = false }) => {
  // Log del cantiereId all'avvio
  console.log('PosaCaviCollegamenti - cantiereId da props:', propCantiereId);

  // Aggiungi navigate per la navigazione programmatica
  const navigate = useNavigate();

  // Se cantiereId non è definito nelle props, prova a recuperarlo dal localStorage
  const cantiereId = propCantiereId || parseInt(localStorage.getItem('selectedCantiereId'), 10);
  console.log('PosaCaviCollegamenti - cantiereId effettivo:', cantiereId);
  const [loading, setLoading] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedCavo, setSelectedCavo] = useState(null);
  const [formData, setFormData] = useState({
    id_cavo: '',
    metri_posati: '',
    id_bobina: ''
  });
  const [cavoIdInput, setCavoIdInput] = useState('');
  const [formErrors, setFormErrors] = useState({});
  const [formWarnings, setFormWarnings] = useState({});
  const [cavi, setCavi] = useState([]);
  const [caviLoading, setCaviLoading] = useState(false);
  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'

  // Inizializza il componente con l'opzione iniziale se specificata
  React.useEffect(() => {
    if (initialOption) {
      console.log('Inizializzazione con:', { initialOption, preselectedCavo, dialogOnly });

      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect
      // per evitare dipendenze circolari
      setSelectedOption(initialOption);

      if (initialOption === 'eliminaCavo') {
        if (preselectedCavo) {
          // Se c'è un cavo preselezionato, usalo direttamente
          console.log('Eliminazione con cavo preselezionato:', preselectedCavo.id_cavo);
          setSelectedCavo(preselectedCavo);
          setDialogType('eliminaCavo');
          setOpenDialog(true);
        } else {
          // Altrimenti carica la lista dei cavi
          loadCavi('eliminaCavo');
          setDialogType('eliminaCavo');
          setOpenDialog(true);
        }
      } else if (initialOption === 'modificaCavo') {
        if (preselectedCavo) {
          // Se c'è un cavo preselezionato, usalo direttamente per la modifica
          console.log('Modifica con cavo preselezionato:', preselectedCavo.id_cavo);
          setSelectedCavo(preselectedCavo);
          setDialogType('modificaCavo');
          setFormData({
            ...preselectedCavo,
            metri_teorici: preselectedCavo.metri_teorici || '',
            metratura_reale: preselectedCavo.metratura_reale || '0'
          });
          setOpenDialog(true);
        } else {
          // Altrimenti carica la lista dei cavi
          loadCavi('modificaCavo');
          setDialogType('selezionaCavo');
          setOpenDialog(true);
        }
      } else if (initialOption === 'aggiungiCavo') {
        setDialogType('aggiungiCavo');
        setOpenDialog(true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialOption, preselectedCavo]);

  // Carica i cavi attivi per la selezione
  const loadCavi = async (operationType) => {
    try {
      setCaviLoading(true);

      // Verifica che cantiereId sia valido
      if (!cantiereId) {
        throw new Error('ID cantiere non valido o mancante');
      }

      console.log('Caricamento cavi per cantiere:', cantiereId);
      const caviData = await caviService.getCavi(cantiereId, 0);

      // Filtra i cavi in base al tipo di operazione
      if (operationType === 'modificaCavo') {
        // Per modifica cavo, mostra solo i cavi non posati (metratura_reale = 0 e stato != Installato)
        const caviNonPosati = caviData.filter(cavo =>
          parseFloat(cavo.metratura_reale) === 0 &&
          cavo.stato_installazione !== 'Installato'
        );
        setCavi(caviNonPosati);
      } else {
        // Per altre operazioni, mostra tutti i cavi
        setCavi(caviData);
      }
    } catch (error) {
      console.error('Errore nel caricamento dei cavi:', error);

      // Gestione più dettagliata dell'errore
      let errorMessage = 'Errore nel caricamento dei cavi';

      if (error.response) {
        // Errore dal server con risposta
        errorMessage += `: ${error.response.status} ${error.response.statusText}`;
        if (error.response.data && error.response.data.detail) {
          errorMessage += ` - ${error.response.data.detail}`;
        }
      } else if (error.request) {
        // Errore di rete senza risposta dal server
        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';
      } else if (error.message) {
        // Errore con messaggio
        errorMessage += `: ${error.message}`;
      }

      onError(errorMessage);
    } finally {
      setCaviLoading(false);
    }
  };

  // Gestisce la selezione di un'opzione dal menu
  const handleOptionSelect = (option) => {
    setSelectedOption(option);

    if (option === 'inserisciMetri' || option === 'modificaBobina') {
      loadCavi(option);
      setDialogType(option);
      setOpenDialog(true);
    } else if (option === 'aggiungiCavo') {
      // Apri il dialog per aggiungere un nuovo cavo
      setDialogType('aggiungiCavo');
      setOpenDialog(true);
    } else if (option === 'modificaCavo') {
      loadCavi('modificaCavo');
      setDialogType('selezionaCavo');
      setOpenDialog(true);
    } else if (option === 'eliminaCavo') {
      loadCavi('eliminaCavo');
      setDialogType('eliminaCavo');
      setOpenDialog(true);
    }
  };

  // Gestisce la chiusura del dialog
  const handleCloseDialog = () => {
    console.log('Chiusura dialog...');
    // Reset dello stato del componente
    setOpenDialog(false);
    setSelectedCavo(null);
    setFormData({
      id_cavo: '',
      metri_posati: '',
      id_bobina: ''
    });
    setCavoIdInput('');
    setFormErrors({});
    setFormWarnings({});
    setDeleteMode('spare'); // Reset alla modalità predefinita
    setLoading(false); // Assicurati che loading sia false quando chiudi il dialog

    // NON chiamare onSuccess quando si annulla il dialog
    // Il genitore gestirà la chiusura tramite onClose del Dialog
  };

  // Gestisce la selezione di un cavo
  const handleCavoSelect = (cavo) => {
    setSelectedCavo(cavo);
    if (dialogType === 'inserisciMetri') {
      setFormData({
        ...formData,
        id_cavo: cavo.id_cavo,
        metri_posati: ''
      });
    } else if (dialogType === 'modificaBobina') {
      setFormData({
        ...formData,
        id_cavo: cavo.id_cavo,
        id_bobina: cavo.id_bobina || ''
      });
    } else if (dialogType === 'selezionaCavo') {
      setDialogType('modificaCavo');
      setSelectedCavo(cavo);
      setFormData({
        ...cavo,
        metri_teorici: cavo.metri_teorici || '',
        metratura_reale: cavo.metratura_reale || '0'
      });
    }
  };

  // Gestisce la ricerca di un cavo per ID
  const handleSearchCavoById = async () => {
    if (!cavoIdInput.trim()) {
      onError('Inserisci un ID cavo valido');
      return;
    }

    try {
      setCaviLoading(true);

      // Verifica che cantiereId sia valido
      if (!cantiereId) {
        throw new Error('ID cantiere non valido o mancante');
      }

      console.log('Ricerca cavo con ID:', cavoIdInput, 'per cantiere:', cantiereId);
      const caviData = await caviService.getCavi(cantiereId, 0);
      const cavo = caviData.find(c => c.id_cavo === cavoIdInput.trim());

      if (!cavo) {
        onError(`Cavo con ID ${cavoIdInput} non trovato`);
        return;
      }

      // Verifica se stiamo cercando un cavo per modificarlo
      if (dialogType === 'selezionaCavo') {
        // Verifica che il cavo non sia già posato
        if (parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato') {
          onError(`Il cavo ${cavo.id_cavo} risulta già posato. Utilizza l'opzione "Modifica bobina cavo posato" per modificarlo.`);
          return;
        }
      }

      // Seleziona il cavo trovato
      handleCavoSelect(cavo);
    } catch (error) {
      console.error('Errore nel caricamento dei cavi:', error);

      // Gestione più dettagliata dell'errore
      let errorMessage = 'Errore nel caricamento dei cavi';

      if (error.response) {
        // Errore dal server con risposta
        errorMessage += `: ${error.response.status} ${error.response.statusText}`;
        if (error.response.data && error.response.data.detail) {
          errorMessage += ` - ${error.response.data.detail}`;
        }
      } else if (error.request) {
        // Errore di rete senza risposta dal server
        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';
      } else if (error.message) {
        // Errore con messaggio
        errorMessage += `: ${error.message}`;
      }

      onError(errorMessage);
    } finally {
      setCaviLoading(false);
    }
  };

  // Gestisce il cambio dell'input dell'ID cavo
  const handleCavoIdInputChange = (e) => {
    setCavoIdInput(e.target.value);
  };

  // Gestisce il cambio dei valori nel form con validazione
  const handleFormChange = (e) => {
    const { name, value } = e.target;

    // Aggiorna il valore nel form
    setFormData({
      ...formData,
      [name]: value
    });

    // Valida il campo
    if (dialogType === 'modificaCavo') {
      const additionalParams = {};
      if (name === 'metratura_reale') {
        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);
      }

      const result = validateField(name, value, additionalParams);

      // Aggiorna gli errori
      setFormErrors(prev => ({
        ...prev,
        [name]: !result.valid ? result.message : null
      }));

      // Aggiorna gli avvisi
      setFormWarnings(prev => ({
        ...prev,
        [name]: result.warning ? result.message : null
      }));
    }
  };

  // Gestisce il salvataggio del form con validazione
  const handleSave = async () => {
    try {
      setLoading(true);

      if (dialogType === 'inserisciMetri') {
        // Valida i metri posati
        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {
          setFormErrors({ metri_posati: 'Inserire un valore numerico valido' });
          setLoading(false);
          return;
        }

        try {
          await caviService.updateMetriPosati(
            cantiereId,
            formData.id_cavo,
            parseFloat(formData.metri_posati)
          );
          // Prima chiama onSuccess, poi chiudi il dialog
          onSuccess('Metri posati aggiornati con successo');
          // Chiudi il dialog
          handleCloseDialog();
          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore
          setTimeout(() => {
            redirectToVisualizzaCavi(navigate, 1000);
          }, 500);
        } catch (error) {
          console.error('Errore durante l\'aggiornamento dei metri posati:', error);
          onError('Errore durante l\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));
          setLoading(false);
        }
      } else if (dialogType === 'modificaBobina') {
        try {
          await caviService.updateBobina(
            cantiereId,
            formData.id_cavo,
            formData.id_bobina
          );
          // Prima chiama onSuccess, poi chiudi il dialog
          onSuccess('Bobina aggiornata con successo');
          // Chiudi il dialog
          handleCloseDialog();
          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore
          setTimeout(() => {
            redirectToVisualizzaCavi(navigate, 1000);
          }, 500);
        } catch (error) {
          console.error('Errore durante l\'aggiornamento della bobina:', error);
          onError('Errore durante l\'aggiornamento della bobina: ' + (error.message || 'Errore sconosciuto'));
          setLoading(false);
        }
      } else if (dialogType === 'modificaCavo') {
        // Validazione completa dei dati del cavo
        const validation = validateCavoData(formData);

        if (!validation.isValid) {
          setFormErrors(validation.errors);
          setFormWarnings(validation.warnings);
          setLoading(false);
          return;
        }

        // Usa i dati validati
        const validatedData = validation.validatedData;

        // Rimuovi i campi di sistema che non devono essere modificati
        const dataToSend = { ...validatedData };
        delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema
        delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema
        delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema
        delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema
        delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati

        // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente
        dataToSend.modificato_manualmente = 1;

        console.log('Dati inviati al server:', dataToSend);

        try {
          console.log('Invio dati al server per aggiornamento cavo:', dataToSend);
          const result = await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);
          console.log('Risposta dal server dopo aggiornamento cavo:', result);

          // Prima chiama onSuccess, poi chiudi il dialog
          onSuccess('Cavo modificato con successo');
          // Chiudi il dialog
          handleCloseDialog();
          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore
          setTimeout(() => {
            redirectToVisualizzaCavi(navigate, 1000);
          }, 500);
          return;
        } catch (error) {
          console.error('Errore durante l\'aggiornamento del cavo:', error);

          // Gestione più dettagliata dell'errore
          let errorMessage = 'Errore durante l\'aggiornamento del cavo';

          if (error.response) {
            // Errore dal server con risposta
            errorMessage += `: ${error.response.status} ${error.response.statusText}`;
            if (error.response.data && error.response.data.detail) {
              errorMessage += ` - ${error.response.data.detail}`;
            }
          } else if (error.request) {
            // Errore di rete senza risposta dal server
            errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';

            // Anche se c'è un errore di rete, la modifica potrebbe essere stata salvata
            // Quindi consideriamo l'operazione come riuscita
            console.log('Considerando l\'operazione come riuscita nonostante l\'errore di rete');
            // Prima chiama onSuccess, poi chiudi il dialog
            onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');
            // Chiudi il dialog
            handleCloseDialog();
            // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore
            setTimeout(() => {
              redirectToVisualizzaCavi(navigate, 1000);
            }, 500);
            return;
          } else if (error.message) {
            // Errore con messaggio
            errorMessage += `: ${error.message}`;

            // Se il messaggio indica che la modifica potrebbe essere stata salvata comunque
            if (error.message.includes('La modifica potrebbe essere stata salvata')) {
              console.log('Considerando l\'operazione come riuscita nonostante l\'errore');
              handleCloseDialog();
              onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');
              // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore
              redirectToVisualizzaCavi(navigate, 1000);
              return;
            }
          }

          onError(errorMessage);
          setLoading(false);
          return;
        }

        // Mostra avvisi se presenti
        if (Object.keys(validation.warnings).length > 0) {
          const warningMessages = Object.values(validation.warnings).join('\n');
          console.warn('Avvisi durante il salvataggio:', warningMessages);
        }
      } else if (dialogType === 'eliminaCavo') {
        // Verifica se il cavo è installato
        const isInstalled = selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0);

        if (isInstalled) {
          // Se è installato, marca solo come SPARE
          console.log('Marcando cavo installato come SPARE:', selectedCavo.id_cavo);
          try {
            // Prima prova con markCavoAsSpare
            const result = await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo);
            console.log('Risultato marcatura SPARE:', result);
            console.log('Nuovo valore modificato_manualmente:', result.modificato_manualmente);
            // Chiudi il dialog prima di chiamare onSuccess
            handleCloseDialog();
            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);
            // Reindirizza alla pagina di visualizzazione cavi con un ritardo
            redirectToVisualizzaCavi(navigate, 500);
          } catch (markError) {
            console.error('Errore con markCavoAsSpare, tentativo con deleteCavo mode=spare:', markError);
            // Se fallisce, prova con deleteCavo mode=spare
            const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, 'spare');
            console.log('Risultato marcatura SPARE con deleteCavo:', result);
            // Chiudi il dialog prima di chiamare onSuccess
            handleCloseDialog();
            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);
          }
        } else {
          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)
          console.log('Eliminando cavo non installato con modalità:', deleteMode);
          const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);
          console.log('Risultato eliminazione/marcatura:', result);
          // Chiudi il dialog prima di chiamare onSuccess
          handleCloseDialog();
          onSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);
        }
      }

      // Non chiamare handleCloseDialog() qui, perché il dialog verrà chiuso dal genitore
      // quando viene chiamato onSuccess()
    } catch (error) {
      console.error('Errore durante l\'operazione:', error);

      // Gestione più dettagliata dell'errore
      let errorMessage = 'Errore sconosciuto';

      if (error.detail) {
        // Errore dal backend con dettaglio
        errorMessage = error.detail;
      } else if (error.message) {
        // Errore con messaggio
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        // Errore come stringa
        errorMessage = error;
      }

      onError('Errore durante l\'operazione: ' + errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Renderizza il dialog in base al tipo
  const renderDialog = () => {
    if (dialogType === 'aggiungiCavo') {
      return (
        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              width: '80%',  // Allargato di 2cm (rispetto a sm che è circa 60%)
              minHeight: '80vh', // Allungato di 1cm (aggiungendo altezza minima)
              maxHeight: '90vh',
              overflow: 'auto'
            }
          }}
        >
          <DialogTitle sx={{ pb: 1 }}>Aggiungi Nuovo Cavo</DialogTitle>
          <DialogContent sx={{ pt: 0, pb: 1 }}>
            <Box sx={{ mt: 0 }}>
              <CavoForm
                mode="add"
                cantiereId={cantiereId}
                onSubmit={async (validatedData) => {
                  try {
                    await caviService.createCavo(cantiereId, validatedData);
                    return true;
                  } catch (error) {
                    throw error;
                  }
                }}
                onSuccess={(message) => {
                  onSuccess(message);
                  handleCloseDialog();
                }}
                onError={onError}
                isDialog={true}
                onCancel={handleCloseDialog}
              />
            </Box>
          </DialogContent>
          {/* No DialogActions needed here as CavoForm has its own buttons */}
        </Dialog>
      );
    } else if (dialogType === 'inserisciMetri') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Inserisci Metri Posati</DialogTitle>
          <DialogContent>
            {caviLoading ? (
              <CircularProgress />
            ) : cavi.length === 0 ? (
              <Alert severity="info">Nessun cavo disponibile</Alert>
            ) : !selectedCavo ? (
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Seleziona un cavo:
                </Typography>
                <List>
                  {cavi.map((cavo) => (
                    <ListItem
                      button
                      key={cavo.id_cavo}
                      onClick={() => handleCavoSelect(cavo)}
                    >
                      <ListItemText
                        primary={cavo.id_cavo}
                        secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            ) : (
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Cavo selezionato: {selectedCavo.id_cavo}
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Metri teorici: {selectedCavo.metri_teorici || 'N/A'}
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Metratura attuale: {selectedCavo.metratura_reale || '0'}
                </Typography>
                <TextField
                  margin="dense"
                  name="metri_posati"
                  label="Metri posati da aggiungere"
                  type="number"
                  fullWidth
                  variant="outlined"
                  value={formData.metri_posati}
                  onChange={handleFormChange}
                  required
                  error={!!formErrors.metri_posati}
                  helperText={formErrors.metri_posati}
                  sx={{ mt: 2 }}
                />
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            {selectedCavo && (
              <Button
                onClick={handleSave}
                disabled={loading || !formData.metri_posati}
                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
              >
                Salva
              </Button>
            )}
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'modificaBobina') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>Modifica Bobina Cavo Posato</DialogTitle>
          <DialogContent>
            {caviLoading ? (
              <CircularProgress />
            ) : cavi.length === 0 ? (
              <Alert severity="info">Nessun cavo disponibile</Alert>
            ) : !selectedCavo ? (
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Seleziona un cavo:
                </Typography>
                <List>
                  {cavi.map((cavo) => (
                    <ListItem
                      button
                      key={cavo.id_cavo}
                      onClick={() => handleCavoSelect(cavo)}
                    >
                      <ListItemText
                        primary={cavo.id_cavo}
                        secondary={`Bobina attuale: ${cavo.id_bobina ? (cavo.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : cavo.id_bobina) : 'BOBINA VUOTA'}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </Box>
            ) : (
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Cavo selezionato: {selectedCavo.id_cavo}
                </Typography>
                <Typography variant="body2" gutterBottom>
                  Bobina attuale: {selectedCavo.id_bobina ? (selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : selectedCavo.id_bobina) : 'BOBINA VUOTA'}
                </Typography>
                <TextField
                  margin="dense"
                  name="id_bobina"
                  label="ID Bobina"
                  fullWidth
                  variant="outlined"
                  value={formData.id_bobina}
                  onChange={handleFormChange}
                  sx={{ mt: 2 }}
                />
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            {selectedCavo && (
              <Button
                onClick={handleSave}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
              >
                Salva
              </Button>
            )}
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'selezionaCavo') {
      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>Modifica Cavo</DialogTitle>
          <DialogContent>
            <Alert severity="info" sx={{ mb: 2 }}>
              Puoi modificare solo i cavi non ancora posati (metratura = 0 e stato diverso da "Installato").
              Per modificare cavi già posati, utilizzare l'opzione "Modifica bobina cavo posato".
            </Alert>

            {caviLoading ? (
              <CircularProgress />
            ) : !selectedCavo ? (
              <Box sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Inserisci l'ID del cavo da modificare:
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                  <TextField
                    fullWidth
                    label="ID Cavo"
                    variant="outlined"
                    value={cavoIdInput}
                    onChange={handleCavoIdInputChange}
                    placeholder="Inserisci l'ID del cavo"
                  />
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleSearchCavoById}
                    disabled={caviLoading || !cavoIdInput.trim()}
                    sx={{ ml: 2, minWidth: '120px' }}
                  >
                    {caviLoading ? <CircularProgress size={24} /> : "Cerca"}
                  </Button>
                </Box>
              </Box>
            ) : null}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'eliminaCavo') {
      // Verifica se il cavo selezionato è installato
      const isInstalled = selectedCavo && (selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0));

      return (
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            {!selectedCavo ? 'Elimina Cavo' :
             isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'}
          </DialogTitle>
          <DialogContent>
            {caviLoading ? (
              <CircularProgress />
            ) : !selectedCavo ? (
              <Box sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Inserisci l'ID del cavo da eliminare:
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                  <TextField
                    fullWidth
                    label="ID Cavo"
                    variant="outlined"
                    value={cavoIdInput}
                    onChange={handleCavoIdInputChange}
                    placeholder="Inserisci l'ID del cavo"
                  />
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleSearchCavoById}
                    disabled={caviLoading || !cavoIdInput.trim()}
                    sx={{ ml: 2, minWidth: '120px' }}
                  >
                    {caviLoading ? <CircularProgress size={24} /> : "Cerca"}
                  </Button>
                </Box>
              </Box>
            ) : dialogType === 'eliminaCavo' && isInstalled ? (
              <>
                <DialogContentText>
                  Il cavo <strong>{selectedCavo.id_cavo}</strong> risulta installato o parzialmente posato.
                  {selectedCavo.metratura_reale > 0 && (
                    <> Metri posati: <strong>{selectedCavo.metratura_reale} m</strong>.</>
                  )}
                </DialogContentText>
                <DialogContentText sx={{ mt: 2 }}>
                  Non è possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?
                </DialogContentText>
              </>
            ) : dialogType === 'eliminaCavo' ? (
              <>
                <DialogContentText>
                  Stai per eliminare il cavo <strong>{selectedCavo.id_cavo}</strong>.
                </DialogContentText>

                <FormControl component="fieldset" sx={{ mt: 2 }}>
                  <FormLabel component="legend">Scegli l'operazione da eseguire:</FormLabel>
                  <RadioGroup
                    value={deleteMode}
                    onChange={(e) => setDeleteMode(e.target.value)}
                  >
                    <FormControlLabel
                      value="spare"
                      control={<Radio />}
                      label="Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)"
                    />
                    <FormControlLabel
                      value="delete"
                      control={<Radio />}
                      label="Elimina definitivamente (rimuove completamente il cavo dal database)"
                    />
                  </RadioGroup>
                </FormControl>
              </>
            ) : null}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Annulla</Button>
            {dialogType === 'eliminaCavo' && selectedCavo && (
              <Button
                onClick={handleSave}
                disabled={loading}
                color={isInstalled ? "warning" : "error"}
                startIcon={loading ? <CircularProgress size={20} /> : isInstalled ? <WarningIcon /> : <DeleteIcon />}
              >
                {isInstalled ? "Marca come SPARE" : (deleteMode === 'spare' ? "Marca come SPARE" : "Elimina definitivamente")}
              </Button>
            )}
          </DialogActions>
        </Dialog>
      );
    } else if (dialogType === 'modificaCavo') {
      return (
        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: {
              width: '80%',  // Allargato di 2cm (rispetto a sm che è circa 60%)
              minHeight: '80vh', // Allungato di 1cm (aggiungendo altezza minima)
              maxHeight: '90vh',
              overflow: 'auto'
            }
          }}
        >
          <DialogTitle sx={{ pb: 1 }}>Modifica Cavo</DialogTitle>
          <DialogContent sx={{ pt: 0, pb: 1 }}>
            <Box sx={{ mt: 0 }}>
              {selectedCavo ? (
                <CavoForm
                  mode="edit"
                  initialData={formData}
                  cantiereId={cantiereId}
                  onSubmit={async (validatedData) => {
                    try {
                      // Rimuovi i campi di sistema che non devono essere modificati
                      const dataToSend = { ...validatedData };
                      delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema
                      delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema
                      delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema
                      delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema
                      delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati

                      // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente
                      dataToSend.modificato_manualmente = 1;

                      await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);
                      return true;
                    } catch (error) {
                      throw error;
                    }
                  }}
                  onSuccess={(message) => {
                    onSuccess(message);
                    handleCloseDialog();
                  }}
                  onError={onError}
                  isDialog={true}
                  onCancel={handleCloseDialog}
                />
              ) : (
                <SelezionaCavoForm
                  cantiereId={cantiereId}
                  onSuccess={(message) => {
                    onSuccess(message);
                    handleCloseDialog();
                  }}
                  onError={onError}
                  isDialog={true}
                  onCancel={handleCloseDialog}
                />
              )}
            </Box>
          </DialogContent>
          {/* No DialogActions needed here as CavoForm has its own buttons */}
        </Dialog>
      );
    }

    return null;
  };

  // Se è in modalità dialogOnly, renderizza solo il dialog
  if (dialogOnly) {
    return renderDialog();
  }

  return (
    <Box sx={{ display: 'flex' }}>
      {/* Menu a cascata nella sidebar */}
      <Box sx={{ width: '280px', mr: 3 }}>
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Posa Cavi e Collegamenti
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <List component="nav" dense>
            <ListItemButton onClick={() => handleOptionSelect('inserisciMetri')}>
              <ListItemIcon>
                <CableIcon />
              </ListItemIcon>
              <ListItemText primary="1. Inserisci metri posati" />
            </ListItemButton>

            <ListItemButton onClick={() => handleOptionSelect('modificaBobina')}>
              <ListItemIcon>
                <EditIcon />
              </ListItemIcon>
              <ListItemText primary="2. Modifica bobina cavo posato" />
            </ListItemButton>

            <ListItemButton onClick={() => handleOptionSelect('aggiungiCavo')}>
              <ListItemIcon>
                <AddIcon />
              </ListItemIcon>
              <ListItemText primary="3. Aggiungi nuovo cavo" />
            </ListItemButton>

            <ListItemButton onClick={() => handleOptionSelect('modificaCavo')}>
              <ListItemIcon>
                <EditIcon />
              </ListItemIcon>
              <ListItemText primary="4. Modifica cavo" />
            </ListItemButton>

            <ListItemButton onClick={() => handleOptionSelect('eliminaCavo')}>
              <ListItemIcon>
                <DeleteIcon />
              </ListItemIcon>
              <ListItemText primary="5. Elimina cavo" />
            </ListItemButton>
          </List>
        </Paper>
      </Box>

      {/* Area principale per il contenuto */}
      <Box sx={{ flexGrow: 1 }}>
        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          {!selectedOption && (
            <Typography variant="body1">
              Seleziona un'opzione dal menu a sinistra per iniziare.
            </Typography>
          )}
          {selectedOption && !openDialog && (
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                {selectedOption === 'inserisciMetri' && 'Inserisci metri posati'}
                {selectedOption === 'modificaCavo' && 'Modifica cavo'}
                {selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo'}
                {selectedOption === 'eliminaCavo' && 'Elimina cavo'}
                {selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato'}
              </Typography>
              <Typography variant="body1">
                Caricamento in corso...
              </Typography>
              <CircularProgress sx={{ mt: 2 }} />
            </Box>
          )}
        </Paper>
      </Box>

      {renderDialog()}
    </Box>
  );
};

export default PosaCaviCollegamenti;
