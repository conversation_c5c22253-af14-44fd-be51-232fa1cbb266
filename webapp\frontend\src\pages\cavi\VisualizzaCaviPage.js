import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  Alert,
  IconButton,
  Chip,
  CircularProgress,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import {
  Cable as CableIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Link as LinkIcon,
  LinkOff as LinkOffIcon,
  Timeline as TimelineIcon,
  CheckBox as CheckBoxIcon,
  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  SelectAll as SelectAllIcon,
  ContentCopy as CopyIcon,
  Settings as SettingsIcon,
  Verified as VerifiedIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useGlobalContext } from '../../context/GlobalContext';
import PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';
import caviService from '../../services/caviService';
import parcoCaviService from '../../services/parcoCaviService';
import CavoForm from '../../components/cavi/CavoForm';
import { normalizeInstallationStatus } from '../../utils/validationUtils';
import CaviFilterableTable from '../../components/cavi/CaviFilterableTable';
import InserisciMetriDialog from '../../components/cavi/InserisciMetriDialog';
import ModificaBobinaDialog from '../../components/cavi/ModificaBobinaDialog';
import CollegamentiCavo from '../../components/cavi/CollegamentiCavo';

import './CaviPage.css';

const VisualizzaCaviPage = () => {
  const { isImpersonating, user } = useAuth();
  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();
  const navigate = useNavigate();
  const [cantiereId, setCantiereId] = useState(null);
  const [cantiereName, setCantiereName] = useState('');
  const [caviAttivi, setCaviAttivi] = useState([]);
  const [caviSpare, setCaviSpare] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  // Stato per le notifiche
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  // Rimosso stato viewMode

  // Stato per il dialogo dei dettagli del cavo
  const [selectedCavo, setSelectedCavo] = useState(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);

  // Stati per la selezione dei cavi
  const [selectionEnabled, setSelectionEnabled] = useState(false);
  const [selectedCaviAttivi, setSelectedCaviAttivi] = useState([]);
  const [selectedCaviSpare, setSelectedCaviSpare] = useState([]);

  // Stati per i dialoghi di azione sui pulsanti stato
  const [inserisciMetriDialog, setInserisciMetriDialog] = useState({
    open: false,
    cavo: null,
    loading: false
  });

  const [collegamentiDialog, setCollegamentiDialog] = useState({
    open: false,
    cavo: null,
    loading: false
  });
  const [modificaBobinaDialog, setModificaBobinaDialog] = useState({
    open: false,
    cavo: null,
    loading: false
  });

  const [bobineDisponibili, setBobineDisponibili] = useState([]);

  // Stato per il cavo selezionato dal menu contestuale
  const [selectedCavoForAction, setSelectedCavoForAction] = useState(null);

  // Stati per statistiche avanzate
  const [statistics, setStatistics] = useState({
    totaleCavi: 0,
    caviInstallati: 0,
    caviDaInstallare: 0,
    caviInCorso: 0,
    caviCollegati: 0,
    caviNonCollegati: 0,
    caviCertificati: 0,
    caviNonCertificati: 0,
    percentualeInstallazione: 0,
    percentualeCollegamento: 0,
    percentualeCertificazione: 0,
    metriTotali: 0,
    metriInstallati: 0,
    metriRimanenti: 0
  });



  // Stato per la gestione delle revisioni
  const [revisioniDisponibili, setRevisioniDisponibili] = useState([]);
  const [revisioneSelezionata, setRevisioneSelezionata] = useState('');
  const [revisioneCorrente, setRevisioneCorrente] = useState('');

  // Rimosso stato per il debug

  // Funzione per calcolare le statistiche avanzate
  const calculateStatistics = (caviAttiviData, caviSpareData) => {
    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];

    if (tuttiCavi.length === 0) {
      console.log('Nessun cavo disponibile per il calcolo delle statistiche');
      return;
    }

    console.log('Calcolo statistiche con dati:', {
      caviAttivi: caviAttiviData?.length || 0,
      caviSpare: caviSpareData?.length || 0,
      totale: tuttiCavi.length
    });

    const totaleCavi = tuttiCavi.length;

    // Calcola stati di installazione
    const caviInstallati = tuttiCavi.filter(cavo =>
      cavo.stato_installazione === 'Installato' ||
      cavo.stato_installazione === 'INSTALLATO' ||
      cavo.stato_installazione === 'POSATO'
    ).length;

    const caviDaInstallare = tuttiCavi.filter(cavo =>
      cavo.stato_installazione === 'Da installare' ||
      cavo.stato_installazione === 'DA_INSTALLARE'
    ).length;

    const caviInCorso = tuttiCavi.filter(cavo =>
      cavo.stato_installazione === 'In corso' ||
      cavo.stato_installazione === 'IN_CORSO'
    ).length;

    // Calcola stati di collegamento
    const caviCollegati = tuttiCavi.filter(cavo =>
      cavo.collegamenti === 3 &&
      cavo.responsabile_partenza &&
      cavo.responsabile_arrivo
    ).length;

    const caviNonCollegati = totaleCavi - caviCollegati;

    // Calcola percentuali
    const percentualeInstallazione = totaleCavi > 0 ? Math.round((caviInstallati / totaleCavi) * 100) : 0;
    const percentualeCollegamento = totaleCavi > 0 ? Math.round((caviCollegati / totaleCavi) * 100) : 0;

    // Calcola metri
    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);
    const metriInstallati = tuttiCavi
      .filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO')
      .reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);
    const metriRimanenti = metriTotali - metriInstallati;

    // Calcola certificazioni (per ora placeholder, sarà implementato)
    const caviCertificati = 0; // TODO: Implementare caricamento certificazioni
    const caviNonCertificati = totaleCavi - caviCertificati;
    const percentualeCertificazione = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;

    const newStatistics = {
      totaleCavi,
      caviInstallati,
      caviDaInstallare,
      caviInCorso,
      caviCollegati,
      caviNonCollegati,
      caviCertificati,
      caviNonCertificati,
      percentualeInstallazione,
      percentualeCollegamento,
      percentualeCertificazione,
      metriTotali: Math.round(metriTotali),
      metriInstallati: Math.round(metriInstallati),
      metriRimanenti: Math.round(metriRimanenti)
    };

    console.log('Nuove statistiche calcolate:', newStatistics);
    setStatistics(newStatistics);
  };

  // Funzione per caricare gli stati di installazione disponibili
  const loadStatiInstallazione = () => {
    // Usa i valori dell'enum StatoInstallazione
    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);
  };

  // Funzione per caricare le revisioni disponibili
  const loadRevisioni = async (cantiereIdToUse) => {
    try {
      console.log('Caricamento revisioni per cantiere:', cantiereIdToUse);

      // Carica la revisione corrente
      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);
      console.log('Revisione corrente:', revisioneCorrenteData);
      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);

      // Carica tutte le revisioni disponibili
      const revisioniData = await caviService.getRevisioniDisponibili(cantiereIdToUse);
      console.log('Revisioni disponibili:', revisioniData);
      setRevisioniDisponibili(revisioniData.revisioni || []);

      // LOGICA REVISIONI: La revisione corrente è quella di default
      // Non impostiamo una revisione selezionata, così il sistema usa automaticamente la corrente
      console.log('Logica revisioni: usando revisione corrente di default');
    } catch (error) {
      console.error('Errore nel caricamento delle revisioni:', error);
    }
  };

  // Funzione per gestire il cambio di revisione
  const handleRevisioneChange = (event) => {
    const nuovaRevisione = event.target.value;

    // LOGICA REVISIONI:
    // - Se vuoto o "corrente" -> usa revisione corrente (non specificare parametro)
    // - Se specifica -> usa quella revisione per visualizzazione storica
    if (nuovaRevisione === '' || nuovaRevisione === 'corrente') {
      setRevisioneSelezionata('');
      console.log('Passaggio a revisione corrente (default)');
    } else {
      setRevisioneSelezionata(nuovaRevisione);
      console.log('Passaggio a revisione storica:', nuovaRevisione);
    }
  };

  // Stato per filtri e ordinamento
  const [filters, setFilters] = useState({
    stato_installazione: '',
    tipologia: '',
    sort_by: '',
    sort_order: 'asc'
  });

  // Opzioni per i filtri
  const [statiInstallazione, setStatiInstallazione] = useState([]);
  const [tipologieCavi, setTipologieCavi] = useState([]);

  // Rimossa funzione di debug

  // Funzione per caricare i cavi
  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento
  const fetchCavi = async (silentLoading = false) => {
    try {
      if (!silentLoading) {
        setLoading(true);
      }
      console.log('Caricamento cavi per cantiere:', cantiereId);

      // Verifica che cantiereId sia valido
      if (!cantiereId) {
        console.error('fetchCavi: cantiereId non valido:', cantiereId);
        setError('ID cantiere non valido o mancante. Ricarica la pagina.');
        setLoading(false);
        return;
      }

      // Recupera il cantiereId dal localStorage come fallback
      let cantiereIdToUse = cantiereId;
      if (!cantiereIdToUse) {
        cantiereIdToUse = localStorage.getItem('selectedCantiereId');
        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);
        if (!cantiereIdToUse) {
          console.error('Impossibile trovare un ID cantiere valido');
          setError('ID cantiere non trovato. Ricarica la pagina.');
          setLoading(false);
          return;
        }
      }

      // Carica i cavi attivi
      console.log('Caricamento cavi attivi (tipo_cavo=0)...');
      let attivi = [];
      try {
        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);
        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);
      } catch (attiviError) {
        console.error('Errore nel caricamento dei cavi attivi:', attiviError);
        // Continua con un array vuoto
        attivi = [];
      }

      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi
      if (attivi && attivi.length > 0) {
        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);
        if (caviSpareTraAttivi.length > 0) {
          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);
        }
      }

      setCaviAttivi(attivi || []);

      // Carica i cavi SPARE con la nuova funzione dedicata
      let spare = [];
      try {
        console.log('Caricamento cavi SPARE con funzione dedicata...');
        spare = await caviService.getCaviSpare(cantiereIdToUse);
        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);
        if (spare && spare.length > 0) {
          console.log('Primo cavo SPARE:', spare[0]);
        }
      } catch (spareError) {
        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);
        // Se fallisce, prova con il metodo standard
        try {
          console.log('Tentativo con metodo standard...');
          spare = await caviService.getCavi(cantiereIdToUse, 3);
          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);
        } catch (standardError) {
          console.error('Errore anche con metodo standard:', standardError);
          // Continua con un array vuoto
          spare = [];
        }
      }
      setCaviSpare(spare || []);



      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti
      setError('');
    } catch (error) {
      console.error('Errore generale nel caricamento dei cavi:', error);
      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);

      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste
      setTimeout(() => {
        // Verifica se siamo ancora in errore
        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {
          console.log('Errore persistente, tentativo di ricaricamento della pagina...');
          window.location.reload();
        }
      }, 5000); // 5 secondi di ritardo
    } finally {
      if (!silentLoading) {
        setLoading(false);
      }
    }
  };

  // Carica i dati del cantiere e dei cavi
  useEffect(() => {
    // Carica gli stati di installazione all'avvio
    loadStatiInstallazione();

    const fetchData = async () => {
      try {
        console.log('Inizializzazione VisualizzaCaviPage...');

        // Verifica che l'utente sia autenticato
        const token = localStorage.getItem('token');
        console.log('Token presente:', !!token);
        if (!token) {
          setError('Sessione scaduta. Effettua nuovamente il login.');
          setLoading(false);
          return;
        }

        // Recupera l'ID del cantiere selezionato dal localStorage
        let selectedCantiereId = localStorage.getItem('selectedCantiereId');
        let selectedCantiereName = localStorage.getItem('selectedCantiereName');

        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });
        console.log('Dati utente:', user);

        // Stampa tutti i dati nel localStorage per debug
        console.log('DEBUG - Tutti i dati nel localStorage:');
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          console.log(`${key}: ${localStorage.getItem(key)}`);
        }

        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT
        if (user?.role === 'cantieri_user') {
          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');

          // Verifica se l'utente ha un ID cantiere nei dati utente
          if (user.cantiere_id) {
            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);
            selectedCantiereId = user.cantiere_id.toString();
            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;

            // Salva l'ID e il nome del cantiere nel localStorage
            localStorage.setItem('selectedCantiereId', selectedCantiereId);
            localStorage.setItem('selectedCantiereName', selectedCantiereName);
            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);
          } else {
            // Tentativo di recupero dal token JWT
            try {
              console.log('Tentativo di decodifica del token JWT per recuperare l\'ID cantiere');
              const token = localStorage.getItem('token');
              if (token) {
                // Decodifica il token JWT (senza verifica della firma)
                const base64Url = token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {
                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));

                const payload = JSON.parse(jsonPayload);
                console.log('Payload del token JWT:', payload);

                if (payload.cantiere_id) {
                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);
                  selectedCantiereId = payload.cantiere_id.toString();
                  // Usa un nome generico se non disponibile
                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;

                  // Salva l'ID e il nome del cantiere nel localStorage
                  localStorage.setItem('selectedCantiereId', selectedCantiereId);
                  localStorage.setItem('selectedCantiereName', selectedCantiereName);
                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);
                }
              }
            } catch (e) {
              console.error('Errore durante la decodifica del token JWT:', e);
            }
          }
        }

        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug
        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {
          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');
          // Usa il primo cantiere disponibile (questo è solo per debug)
          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database
          selectedCantiereName = 'Cantiere Debug';

          // Salva l'ID e il nome del cantiere nel localStorage
          localStorage.setItem('selectedCantiereId', selectedCantiereId);
          localStorage.setItem('selectedCantiereName', selectedCantiereName);
          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);
        }

        // Verifica finale
        if (!selectedCantiereId) {
          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');
          setLoading(false);
          return;
        }

        // Verifica che l'ID del cantiere sia un numero valido
        const cantiereIdNum = parseInt(selectedCantiereId, 10);
        console.log('ID cantiere convertito a numero:', cantiereIdNum);
        if (isNaN(cantiereIdNum)) {
          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);
          setLoading(false);
          return;
        }

        // Usa il numero convertito, non la stringa
        setCantiereId(cantiereIdNum);
        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);

        // Carica le revisioni disponibili
        await loadRevisioni(cantiereIdNum);



        // Carica i cavi attivi con gestione degli errori migliorata
        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);
        try {
          // Imposta un timeout per evitare che la richiesta rimanga bloccata
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi
          });

          // Esegui la richiesta con un timeout di sicurezza e applica i filtri
          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);
          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);
          const attivi = await Promise.race([caviPromise, timeoutPromise]);

          console.log('Cavi attivi caricati:', attivi);
          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);
          if (attivi && attivi.length > 0) {
            console.log('Primo cavo attivo:', attivi[0]);
          } else {
            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);
          }
          setCaviAttivi(attivi || []);

          // Calcola le statistiche dopo aver caricato i cavi attivi
          calculateStatistics(attivi || [], caviSpare);
        } catch (caviError) {
          console.error('Errore nel caricamento dei cavi attivi:', caviError);
          console.error('Dettagli errore cavi attivi:', {
            message: caviError.message,
            status: caviError.status,
            data: caviError.data,
            stack: caviError.stack,
            code: caviError.code,
            name: caviError.name,
            response: caviError.response ? {
              status: caviError.response.status,
              statusText: caviError.response.statusText,
              data: caviError.response.data
            } : 'No response'
          });

          // Non interrompere il flusso, continua con i cavi spare
          setCaviAttivi([]);
          console.warn('Continuazione del flusso dopo errore nei cavi attivi');

          // Aggiungi un messaggio di errore visibile all'utente
          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);
        }

        // Carica i cavi spare con gestione degli errori migliorata
        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);
        try {
          // Imposta un timeout per evitare che la richiesta rimanga bloccata
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi
          });

          // Esegui la richiesta con un timeout di sicurezza
          console.log('Iniziando chiamata API per cavi spare...');
          // Non applichiamo i filtri ai cavi spare, solo agli attivi
          const sparePromise = caviService.getCavi(cantiereIdNum, 3);
          const spare = await Promise.race([sparePromise, timeoutPromise]);

          console.log('Cavi spare caricati:', spare);
          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);
          if (spare && spare.length > 0) {
            console.log('Primo cavo spare:', spare[0]);
          } else {
            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);
          }
          setCaviSpare(spare || []);

          // Calcola le statistiche dopo aver caricato i cavi spare
          calculateStatistics(caviAttivi, spare || []);
        } catch (spareError) {
          console.error('Errore nel caricamento dei cavi spare:', spareError);
          console.error('Dettagli errore cavi spare:', {
            message: spareError.message,
            status: spareError.status,
            data: spareError.data,
            stack: spareError.stack,
            code: spareError.code,
            name: spareError.name,
            response: spareError.response ? {
              status: spareError.response.status,
              statusText: spareError.response.statusText,
              data: spareError.response.data
            } : 'No response'
          });

          // Non interrompere il flusso, imposta un array vuoto
          setCaviSpare([]);

          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi
          if (!error) {
            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);
          }
        }

        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base
        setLoading(false);

      } catch (err) {
        console.error('Errore nel caricamento dei cavi:', err);
        console.error('Dettagli errore generale:', {
          message: err.message,
          status: err.status || err.response?.status,
          data: err.data || err.response?.data,
          stack: err.stack
        });

        // Estrai il messaggio di errore dettagliato
        let errorMessage = 'Errore sconosciuto';

        if (err.message && err.message.includes('ID cantiere non valido')) {
          errorMessage = err.message;
        } else if (err.status === 401 || err.status === 403 ||
                  err.response?.status === 401 || err.response?.status === 403) {
          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';
        } else if (err.response?.data?.detail) {
          // Estrai il messaggio di errore dettagliato dall'API
          errorMessage = `Errore API: ${err.response.data.detail}`;
        } else if (err.code === 'ERR_NETWORK') {
          // Errore di rete
          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';
        } else if (err.message) {
          errorMessage = err.message;
        }

        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);

        // Imposta array vuoti per evitare errori di rendering
        setCaviAttivi([]);
        setCaviSpare([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [filters]); // Ricarica i dati quando cambiano i filtri

  // I filtri sono ora gestiti dal componente CaviFilterableTable

  // Funzione per aprire il dialogo dei dettagli del cavo
  const handleOpenDetails = (cavo) => {
    setSelectedCavo(cavo);
    setDetailsDialogOpen(true);
  };

  // Funzione per chiudere il dialogo dei dettagli del cavo
  const handleCloseDetails = () => {
    setDetailsDialogOpen(false);
    setSelectedCavo(null);
  };

  // Funzione per chiudere la notifica
  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  // Funzione per mostrare una notifica
  const showNotification = (message, severity = 'success') => {
    setNotification({ open: true, message, severity });
  };

  // Funzioni per gestire la selezione dei cavi
  const handleSelectionToggle = () => {
    setSelectionEnabled(!selectionEnabled);
    // Pulisci le selezioni quando si disabilita la modalità selezione
    if (selectionEnabled) {
      setSelectedCaviAttivi([]);
      setSelectedCaviSpare([]);
    }
  };

  const handleCaviAttiviSelectionChange = (selectedIds) => {
    setSelectedCaviAttivi(selectedIds);
  };

  const handleCaviSpareSelectionChange = (selectedIds) => {
    setSelectedCaviSpare(selectedIds);
  };

  // Funzione per ottenere tutti i cavi selezionati
  const getAllSelectedCavi = () => {
    const selectedAttiviCavi = caviAttivi.filter(cavo => selectedCaviAttivi.includes(cavo.id_cavo));
    const selectedSpareCavi = caviSpare.filter(cavo => selectedCaviSpare.includes(cavo.id_cavo));
    return [...selectedAttiviCavi, ...selectedSpareCavi];
  };

  // Funzione per ottenere il conteggio totale dei cavi selezionati
  const getTotalSelectedCount = () => {
    return selectedCaviAttivi.length + selectedCaviSpare.length;
  };

  // Funzioni per gestire le azioni del menu contestuale
  const handleContextMenuAction = (cavo, action) => {
    console.log('Azione menu contestuale:', action, 'per cavo:', cavo);

    switch (action) {
      case 'view_details':
        handleOpenDetails(cavo);
        break;
      case 'edit':
        // Apri il dialog di modifica cavo con il cavo preselezionato
        setSelectedCavoForAction(cavo);
        setOpenModificaCavoDialog(true);
        break;
      case 'delete':
        // Apri il dialog di eliminazione cavo con il cavo preselezionato
        setSelectedCavoForAction(cavo);
        setOpenEliminaCavoDialog(true);
        break;
      case 'select':
        if (caviAttivi.some(c => c.id_cavo === cavo.id_cavo)) {
          // È un cavo attivo
          const isSelected = selectedCaviAttivi.includes(cavo.id_cavo);
          if (isSelected) {
            setSelectedCaviAttivi(prev => prev.filter(id => id !== cavo.id_cavo));
          } else {
            setSelectedCaviAttivi(prev => [...prev, cavo.id_cavo]);
          }
        } else {
          // È un cavo spare
          const isSelected = selectedCaviSpare.includes(cavo.id_cavo);
          if (isSelected) {
            setSelectedCaviSpare(prev => prev.filter(id => id !== cavo.id_cavo));
          } else {
            setSelectedCaviSpare(prev => [...prev, cavo.id_cavo]);
          }
        }
        // Abilita automaticamente la modalità selezione se non è già attiva
        if (!selectionEnabled) {
          setSelectionEnabled(true);
        }
        break;
      case 'copy_id':
        navigator.clipboard.writeText(cavo.id_cavo);
        showNotification(`ID cavo ${cavo.id_cavo} copiato negli appunti`, 'success');
        break;
      case 'copy_details':
        const details = `ID: ${cavo.id_cavo}\nTipologia: ${cavo.tipologia}\nSezione: ${cavo.sezione}\nMetri: ${cavo.metri_teorici}`;
        navigator.clipboard.writeText(details);
        showNotification('Dettagli cavo copiati negli appunti', 'success');
        break;
      default:
        console.warn('Azione non riconosciuta:', action);
    }
  };

  // Definizione degli elementi del menu contestuale
  const getContextMenuItems = (cavo) => {
    const isSelected = caviAttivi.some(c => c.id_cavo === cavo?.id_cavo)
      ? selectedCaviAttivi.includes(cavo?.id_cavo)
      : selectedCaviSpare.includes(cavo?.id_cavo);

    return [
      {
        type: 'header',
        label: `Cavo ${cavo?.id_cavo || ''}`
      },
      {
        id: 'view_details',
        label: 'Visualizza Dettagli',
        icon: <VisibilityIcon fontSize="small" />,
        action: 'view_details',
        onClick: handleContextMenuAction
      },
      {
        type: 'divider'
      },
      {
        id: 'edit',
        label: 'Modifica',
        icon: <EditIcon fontSize="small" />,
        action: 'edit',
        onClick: handleContextMenuAction,
        color: 'primary'
      },
      {
        id: 'delete',
        label: 'Elimina',
        icon: <DeleteIcon fontSize="small" />,
        action: 'delete',
        onClick: handleContextMenuAction,
        color: 'error'
      },
      {
        type: 'divider'
      },
      {
        id: 'select',
        label: isSelected ? 'Deseleziona' : 'Seleziona',
        icon: <SelectAllIcon fontSize="small" />,
        action: 'select',
        onClick: handleContextMenuAction,
        color: isSelected ? 'warning' : 'success'
      },
      {
        type: 'divider'
      },
      {
        id: 'copy_id',
        label: 'Copia ID',
        icon: <CopyIcon fontSize="small" />,
        action: 'copy_id',
        onClick: handleContextMenuAction,
        shortcut: 'Ctrl+C'
      },
      {
        id: 'copy_details',
        label: 'Copia Dettagli',
        icon: <CopyIcon fontSize="small" />,
        action: 'copy_details',
        onClick: handleContextMenuAction,
        description: 'Copia ID, tipologia, sezione e metri'
      }
    ];
  };

  // Funzioni per gestire le azioni sui pulsanti stato
  const handleStatusAction = async (cavo, actionType, actionLabel) => {
    console.log('🎯 CLICK PULSANTE STATO RILEVATO!');
    console.log('Azione pulsante stato:', actionType, 'per cavo:', cavo);
    console.log('Action label:', actionLabel);

    if (actionType === 'insert_meters') {
      // Apri il dialogo per inserire i metri posati
      setInserisciMetriDialog({
        open: true,
        cavo: cavo,
        loading: false
      });
    } else if (actionType === 'modify_reel') {
      // Carica le bobine disponibili e apri il dialogo per modificare la bobina
      try {
        console.log('Caricamento bobine per cantiere:', cantiereId);
        const bobine = await parcoCaviService.getBobine(cantiereId);
        console.log('Bobine caricate:', bobine);

        setBobineDisponibili(bobine || []);
        setModificaBobinaDialog({
          open: true,
          cavo: cavo,
          loading: false
        });
      } catch (error) {
        console.error('Errore nel caricamento delle bobine:', error);
        showNotification('Errore nel caricamento delle bobine disponibili', 'error');
      }
    } else if (actionType === 'connect_cable' || actionType === 'connect_arrival' ||
               actionType === 'connect_departure' || actionType === 'disconnect_cable' ||
               actionType === 'manage_connections') {

      // Verifica se il cavo è installato
      if (cavo.stato_installazione !== 'Installato') {
        console.log('Azione collegamenti ignorata per cavo non installato:', cavo.id_cavo);
        showNotification('I collegamenti sono disponibili solo per cavi installati', 'info');
        return;
      }

      // Cavo installato - apri il popup per gestire i collegamenti
      console.log('Apertura popup collegamenti per cavo installato:', cavo.id_cavo, 'azione:', actionType);

      // Usa setTimeout per evitare conflitti di stato
      setTimeout(() => {
        setCollegamentiDialog({
          open: true,
          cavo: cavo,
          loading: false
        });
      }, 50);
    }
  };


  // Funzione per salvare i metri posati
  const handleSaveMetri = async (cavoId, metri) => {
    setInserisciMetriDialog(prev => ({ ...prev, loading: true }));

    try {
      console.log('Salvando metri per cavo:', cavoId, 'metri:', metri, 'cantiere:', cantiereId);

      // Chiamata API reale per aggiornare i metri posati
      const cavoAggiornato = await caviService.updateMetriPosati(
        cantiereId,
        cavoId,
        metri,
        null, // id_bobina - per ora null, può essere gestito separatamente
        true  // force_over
      );

      console.log('Cavo aggiornato:', cavoAggiornato);

      // Aggiorna lo stato locale del cavo
      const updateCavo = (cavi) => cavi.map(cavo =>
        cavo.id_cavo === cavoId
          ? {
              ...cavo,
              metratura_reale: metri,
              stato_installazione: cavoAggiornato.stato_installazione || (metri >= cavo.metri_teorici ? 'INSTALLATO' : 'IN_CORSO')
            }
          : cavo
      );

      setCaviAttivi(updateCavo);
      setCaviSpare(updateCavo);

      // Chiudi il dialogo
      setInserisciMetriDialog({ open: false, cavo: null, loading: false });

      showNotification(`Metri posati aggiornati per il cavo ${cavoId}: ${metri}m`, 'success');

      // Ricarica i dati per essere sicuri
      setTimeout(() => fetchCavi(true), 500);

    } catch (error) {
      console.error('Errore nel salvataggio dei metri:', error);
      const errorMessage = error.detail || error.message || 'Errore nel salvataggio dei metri posati';
      showNotification(errorMessage, 'error');
      setInserisciMetriDialog(prev => ({ ...prev, loading: false }));
    }
  };

  // Funzione per modificare la bobina
  const handleModifyBobina = async (cavoId, nuovaBobinaId, motivazione) => {
    setModificaBobinaDialog(prev => ({ ...prev, loading: true }));

    try {
      console.log('Modificando bobina per cavo:', cavoId, 'nuova bobina:', nuovaBobinaId, 'motivazione:', motivazione, 'cantiere:', cantiereId);

      // Chiamata API reale per aggiornare la bobina
      const cavoAggiornato = await caviService.updateBobina(
        cantiereId,
        cavoId,
        nuovaBobinaId,
        true // force_over
      );

      console.log('Cavo aggiornato con nuova bobina:', cavoAggiornato);

      // Aggiorna lo stato locale del cavo
      const updateCavo = (cavi) => cavi.map(cavo =>
        cavo.id_cavo === cavoId
          ? { ...cavo, id_bobina: nuovaBobinaId }
          : cavo
      );

      setCaviAttivi(updateCavo);
      setCaviSpare(updateCavo);

      // Chiudi il dialogo
      setModificaBobinaDialog({ open: false, cavo: null, loading: false });

      showNotification(`Bobina modificata per il cavo ${cavoId}: ${nuovaBobinaId}`, 'success');

      // Ricarica i dati per essere sicuri
      setTimeout(() => fetchCavi(true), 500);

    } catch (error) {
      console.error('Errore nella modifica della bobina:', error);
      const errorMessage = error.detail || error.message || 'Errore nella modifica della bobina';
      showNotification(errorMessage, 'error');
      setModificaBobinaDialog(prev => ({ ...prev, loading: false }));
    }
  };

  // Funzioni per chiudere i dialoghi
  const handleCloseInserisciMetri = () => {
    if (!inserisciMetriDialog.loading) {
      setInserisciMetriDialog({ open: false, cavo: null, loading: false });
    }
  };

  const handleCloseModificaBobina = () => {
    if (!modificaBobinaDialog.loading) {
      setModificaBobinaDialog({ open: false, cavo: null, loading: false });
    }
  };

  const handleCloseCollegamenti = useCallback(() => {
    if (!collegamentiDialog.loading) {
      setCollegamentiDialog(prev => ({ ...prev, open: false }));
    }
  }, [collegamentiDialog.loading]);



  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale

  // Dashboard minimal con statistiche essenziali per visualizzazione cavi
  const renderDashboard = () => (
    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
      <Stack direction="row" spacing={4} alignItems="center" justifyContent="space-between" flexWrap="wrap">
        {/* Statistiche essenziali in formato compatto */}
        <Stack direction="row" alignItems="center" spacing={1}>
          <CableIcon color="primary" fontSize="small" />
          <Box>
            <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
              {statistics.totaleCavi}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Totale
            </Typography>
          </Box>
        </Stack>

        <Stack direction="row" alignItems="center" spacing={1}>
          <CheckCircleIcon color="success" fontSize="small" />
          <Box>
            <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
              {statistics.caviInstallati}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Installati
            </Typography>
          </Box>
        </Stack>

        <Stack direction="row" alignItems="center" spacing={1}>
          <LinkIcon color="info" fontSize="small" />
          <Box>
            <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
              {statistics.caviCollegati}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Collegati
            </Typography>
          </Box>
        </Stack>

        <Stack direction="row" alignItems="center" spacing={1}>
          <VerifiedIcon color="warning" fontSize="small" />
          <Box>
            <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
              {statistics.caviCertificati}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Certificati
            </Typography>
          </Box>
        </Stack>

        <Stack direction="row" alignItems="center" spacing={1}>
          <Box sx={{
            width: 32,
            height: 32,
            borderRadius: '50%',
            bgcolor: statistics.percentualeInstallazione >= 80 ? 'success.main' :
                     statistics.percentualeInstallazione >= 50 ? 'warning.main' : 'error.main',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Typography variant="caption" fontWeight="bold" color="white">
              {statistics.percentualeInstallazione}%
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" fontWeight="medium" sx={{ lineHeight: 1 }}>
              Installazione
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {statistics.metriInstallati}m installati
            </Typography>
          </Box>
        </Stack>
      </Stack>
    </Paper>
  );

  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable

  // Rimossa funzione handleViewModeChange

  // Renderizza il dialogo dei dettagli del cavo
  const renderDetailsDialog = () => {
    if (!selectedCavo) return null;

    return (
      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth="md" fullWidth>
        <DialogTitle>
          Dettagli Cavo: {selectedCavo.id_cavo}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>Informazioni Generali</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>
                {/* n_conduttori field is now a spare field (kept in DB but hidden in UI) */}
                <Typography variant="body2"><strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>
                {/* sh field is now a spare field (kept in DB but hidden in UI) */}
              </Box>

              <Typography variant="subtitle1" gutterBottom>Partenza</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>Arrivo</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>
              </Box>

              <Typography variant="subtitle1" gutterBottom>Installazione</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>
                <Typography variant="body2"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>
                <Typography variant="body2"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>
                <Typography variant="body2"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>
                <Typography variant="body2"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetails}>Chiudi</Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable



  return (
    <Box className="cavi-page">
      {loading ? (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>
          <CircularProgress size={40} />
          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>
          <Button
            variant="outlined"
            color="primary"
            onClick={() => window.location.reload()}
            sx={{ mt: 2 }}
          >
            Ricarica la pagina
          </Button>
        </Box>
      ) : error ? (
        <Box>
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
            {error.includes('Network Error') && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.
                <br />
                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.
              </Typography>
            )}
          </Alert>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              className="primary-button"
              onClick={() => window.location.reload()}
            >
              Ricarica la pagina
            </Button>
          </Box>
        </Box>
      ) : (
        <Box>
          {/* Selettore Revisione */}
          {revisioniDisponibili.length > 0 && (
            <Paper sx={{ p: 2, mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="h6">Visualizzazione:</Typography>
                <FormControl size="small" sx={{ minWidth: 250 }}>
                  <InputLabel>Revisione da Visualizzare</InputLabel>
                  <Select
                    value={revisioneSelezionata || 'corrente'}
                    onChange={handleRevisioneChange}
                    label="Revisione da Visualizzare"
                  >
                    <MenuItem value="corrente">
                      📋 Revisione Corrente {revisioneCorrente && `(${revisioneCorrente})`}
                    </MenuItem>
                    {revisioniDisponibili.map((rev) => (
                      <MenuItem key={rev.revisione} value={rev.revisione}>
                        📚 {rev.revisione} ({rev.cavi_count} cavi)
                        {rev.revisione === revisioneCorrente && ' - Attuale'}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <Chip
                  label={
                    revisioneSelezionata
                      ? `Storico: ${revisioneSelezionata}`
                      : `Corrente: ${revisioneCorrente || 'N/A'}`
                  }
                  color={revisioneSelezionata ? "secondary" : "primary"}
                  variant="outlined"
                />
              </Box>
            </Paper>
          )}

          {/* Dashboard con statistiche avanzate */}
          {renderDashboard()}

          {/* Sezione Cavi */}
          <Box sx={{ mt: 4 }}>
            {/* Contatore selezione - solo quando ci sono cavi selezionati */}
            {selectionEnabled && getTotalSelectedCount() > 0 && (
              <Box sx={{ mb: 2 }}>
                <Chip
                  label={`${getTotalSelectedCount()} cavi selezionati`}
                  color="primary"
                  variant="outlined"
                />
              </Box>
            )}

            {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}
            {process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && (
              <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>
                {Object.keys(caviAttivi[0]).map(key => (
                  <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>
                ))}
              </Box>
            )}

            <CaviFilterableTable
              cavi={caviAttivi}
              loading={loading}
              onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}
              revisioneCorrente={caviAttivi[0]?.revisione_ufficiale || caviAttivi[0]?.revisione || caviAttivi[0]?.rev}
              selectionEnabled={selectionEnabled}
              selectedCavi={selectedCaviAttivi}
              onSelectionChange={handleCaviAttiviSelectionChange}
              onSelectionToggle={handleSelectionToggle}
              contextMenuItems={getContextMenuItems}
              onContextMenuAction={handleContextMenuAction}
              onStatusAction={handleStatusAction}
            />
            {caviAttivi.length === 0 && !loading && (
              <Alert severity="info" sx={{ mt: 2 }}>
                Nessun cavo attivo trovato. I cavi attivi appariranno qui.
              </Alert>
            )}
          </Box>

          {/* Sezione Cavi Spare */}
          <Box sx={{ mt: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h5">
                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}
              </Typography>
            </Box>
            <CaviFilterableTable
              cavi={caviSpare}
              loading={loading}
              onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}
              selectionEnabled={selectionEnabled}
              selectedCavi={selectedCaviSpare}
              onSelectionChange={handleCaviSpareSelectionChange}
              onSelectionToggle={handleSelectionToggle}
              contextMenuItems={getContextMenuItems}
              onContextMenuAction={handleContextMenuAction}
              onStatusAction={handleStatusAction}
            />
            {caviSpare.length === 0 && !loading && (
              <Alert severity="info" sx={{ mt: 2 }}>
                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.
              </Alert>
            )}
          </Box>

          {/* Rimossa sezione Debug */}

          {/* Dialogo dei dettagli del cavo */}
          {renderDetailsDialog()}

          {/* Dialogo per l'eliminazione dei cavi */}
          <Dialog
            open={openEliminaCavoDialog}
            onClose={() => {
              setOpenEliminaCavoDialog(false);
              setSelectedCavoForAction(null);
            }}
            fullWidth
            maxWidth="md"
            PaperProps={{
              sx: {
                width: '80%',  // Allargato di 2cm (rispetto a sm che è circa 60%)
                minHeight: '80vh', // Allungato di 1cm (aggiungendo altezza minima)
                maxHeight: '90vh',
                overflow: 'auto'
              }
            }}
          >
            <PosaCaviCollegamenti
              cantiereId={cantiereId}
              onSuccess={(message) => {
                // Chiudi il dialogo
                setOpenEliminaCavoDialog(false);
                setSelectedCavoForAction(null);

                // Se c'è un messaggio, è un'operazione completata con successo
                if (message) {
                  // Mostra un messaggio di successo
                  console.log('Operazione completata:', message);
                  // Mostra un messaggio di successo con Snackbar
                  showNotification(message, 'success');
                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi
                  setTimeout(() => {
                    console.log('Ricaricamento dati dopo operazione...');
                    try {
                      // Ricarica i dati invece di ricaricare la pagina
                      fetchCavi(true);
                    } catch (error) {
                      console.error('Errore durante il ricaricamento dei dati:', error);
                      // Se fallisce, prova a ricaricare la pagina
                      window.location.reload();
                    }
                  }, 1000);
                } else {
                  // È un'operazione annullata, non mostrare messaggi
                  console.log('Operazione annullata dall\'utente');
                }
              }}
              onError={(message) => {
                // Mostra un messaggio di errore
                console.error('Errore durante l\'eliminazione del cavo:', message);
                // Mostra un messaggio di errore con Snackbar
                showNotification(`Errore: ${message}`, 'error');
                // Chiudi il dialogo
                setOpenEliminaCavoDialog(false);
                setSelectedCavoForAction(null);
                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata
                fetchCavi(true);
              }}
              initialOption="eliminaCavo"
              preselectedCavo={selectedCavoForAction}
              dialogOnly={true}
            />
          </Dialog>

          {/* Dialogo per la modifica dei cavi */}
          <Dialog
            open={openModificaCavoDialog}
            onClose={() => {
              setOpenModificaCavoDialog(false);
              setSelectedCavoForAction(null);
            }}
            fullWidth
            maxWidth="md"
            PaperProps={{
              sx: {
                width: '80%',  // Allargato di 2cm (rispetto a sm che è circa 60%)
                minHeight: '80vh', // Allungato di 1cm (aggiungendo altezza minima)
                maxHeight: '90vh',
                overflow: 'auto'
              }
            }}
          >
            <PosaCaviCollegamenti
              cantiereId={cantiereId}
              onSuccess={(message) => {
                // Chiudi il dialogo
                setOpenModificaCavoDialog(false);
                setSelectedCavoForAction(null);

                // Se c'è un messaggio, è un'operazione completata con successo
                if (message) {
                  // Mostra un messaggio di successo
                  console.log('Operazione completata:', message);
                  // Mostra un messaggio di successo con Snackbar
                  showNotification(message, 'success');
                  // Ricarica i dati immediatamente
                  console.log('Ricaricamento dati dopo operazione...');
                  // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi
                  setTimeout(() => {
                    try {
                      fetchCavi(true);
                    } catch (error) {
                      console.error('Errore durante il ricaricamento dei dati:', error);
                      // Se fallisce, prova a ricaricare la pagina
                      window.location.reload();
                    }
                  }, 1000);
                } else {
                  // È un'operazione annullata, non mostrare messaggi
                  console.log('Operazione annullata dall\'utente');
                }
              }}
              onError={(message) => {
                // Mostra un messaggio di errore
                console.error('Errore durante la modifica del cavo:', message);
                // Mostra un messaggio di errore con Snackbar
                showNotification(`Errore: ${message}`, 'error');
                // Chiudi il dialogo
                setOpenModificaCavoDialog(false);
                setSelectedCavoForAction(null);
                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata
                console.log('Ricaricamento dati dopo errore...');
                fetchCavi(true);
              }}
              initialOption="modificaCavo"
              preselectedCavo={selectedCavoForAction}
              dialogOnly={true}
            />
          </Dialog>

          {/* Dialogo per l'aggiunta di un nuovo cavo */}
          <Dialog open={openAggiungiCavoDialog} onClose={() => setOpenAggiungiCavoDialog(false)} maxWidth="sm" fullWidth>
            <PosaCaviCollegamenti
              cantiereId={cantiereId}
              onSuccess={(message) => {
                // Chiudi il dialogo
                setOpenAggiungiCavoDialog(false);

                // Se c'è un messaggio, è un'operazione completata con successo
                if (message) {
                  // Mostra un messaggio di successo
                  console.log('Operazione completata:', message);
                  // Mostra un messaggio di successo con Snackbar
                  showNotification(message, 'success');
                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi
                  setTimeout(() => {
                    console.log('Ricaricamento dati dopo operazione...');
                    try {
                      // Ricarica i dati in modalità silenziosa per evitare il "blink" della pagina
                      fetchCavi(true);
                    } catch (error) {
                      console.error('Errore durante il ricaricamento dei dati:', error);
                      // Se fallisce, prova a ricaricare la pagina immediatamente
                      console.log('Tentativo di ricaricamento della pagina...');
                      window.location.reload();
                    }
                  }, 1000);
                } else {
                  // È un'operazione annullata, non mostrare messaggi
                  console.log('Operazione annullata dall\'utente');
                }
              }}
              onError={(message) => {
                // Mostra un messaggio di errore
                console.error('Errore durante l\'aggiunta del cavo:', message);
                // Mostra un messaggio di errore con Snackbar
                showNotification(`Errore: ${message}`, 'error');
                // Chiudi il dialogo
                setOpenAggiungiCavoDialog(false);
                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata
                fetchCavi(true);
              }}
              initialOption="aggiungiCavo"
            />
          </Dialog>

          {/* Dialoghi per azioni sui pulsanti stato */}
          <InserisciMetriDialog
            open={inserisciMetriDialog.open}
            onClose={handleCloseInserisciMetri}
            cavo={inserisciMetriDialog.cavo}
            onSave={handleSaveMetri}
            loading={inserisciMetriDialog.loading}
          />

          <ModificaBobinaDialog
            open={modificaBobinaDialog.open}
            onClose={handleCloseModificaBobina}
            cavo={modificaBobinaDialog.cavo}
            bobineDisponibili={bobineDisponibili}
            onSave={handleModifyBobina}
            loading={modificaBobinaDialog.loading}
          />

          {/* Dialog per la gestione collegamenti */}
          <Dialog
            open={collegamentiDialog.open}
            onClose={handleCloseCollegamenti}
            maxWidth="md"
            fullWidth
            disableEscapeKeyDown={false}
            keepMounted={false}
            disablePortal={false}
            disableScrollLock={true}
            hideBackdrop={false}
            disableAutoFocus={true}
            disableEnforceFocus={true}
            disableRestoreFocus={true}
            transitionDuration={0}
            TransitionProps={{
              timeout: 0,
              appear: false,
              enter: false,
              exit: false
            }}
            PaperProps={{
              style: {
                transition: 'none',
                transform: 'none'
              }
            }}
          >
            <DialogContent>
              {collegamentiDialog.cavo && (
                <CollegamentiCavo
                  cantiereId={cantiereId}
                  selectedCavo={collegamentiDialog.cavo}
                  onSuccess={(message) => {
                    if (message) {
                      showNotification(message, 'success');
                      // Chiudi il dialog immediatamente
                      setCollegamentiDialog(prev => ({ ...prev, open: false }));
                      // Ricarica i dati per aggiornare lo stato dei collegamenti
                      setTimeout(() => fetchCavi(true), 300);
                    }
                    // Non chiudere il dialog se message è null (annullamento)
                  }}
                  onError={(message) => {
                    showNotification(message, 'error');
                  }}
                  onClose={handleCloseCollegamenti}
                />
              )}
            </DialogContent>
          </Dialog>

          {/* Snackbar per le notifiche */}
          <Snackbar
            open={notification.open}
            autoHideDuration={4000}
            onClose={handleCloseNotification}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          >
            <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
              {notification.message}
            </Alert>
          </Snackbar>
        </Box>
      )}
    </Box>
  );
};

export default VisualizzaCaviPage;
