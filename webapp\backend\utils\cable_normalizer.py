"""
Sistema di normalizzazione completo e sicuro per tutti i campi del sistema CMS.
Previene SQL injection, XSS, e garantisce consistenza dei dati.
Versione integrata nel backend per uso automatico.
"""

import re
import logging
import html
import unicodedata
from typing import Tuple, Optional, Dict, Any, List, Set
from difflib import SequenceMatcher

class CableNormalizer:
    """
    Classe per la normalizzazione completa e sicura di tutti i campi del sistema CMS.

    Gestisce:
    - Sicurezza: Prevenzione SQL injection, XSS, caratteri pericolosi
    - Sezioni cavi: Case sensitivity, separatori decimali, unità di misura, suffissi
    - Campi testo: Normalizzazione maiuscolo, rimozione spazi, caratteri speciali
    - Consistenza: Standardizzazione di tutti i formati di input
    - Validazione: Controllo lunghezza e caratteri permessi
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Pattern di sicurezza - caratteri pericolosi da rimuovere/sostituire
        self.dangerous_patterns = {
            # SQL injection patterns
            'sql_injection': re.compile(r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b|--|\/\*|\*\/|;|\'|"|`)', re.IGNORECASE),
            # XSS patterns
            'xss': re.compile(r'(<script|</script|javascript:|vbscript:|onload=|onerror=|onclick=|onmouseover=)', re.IGNORECASE),
            # Caratteri di controllo e non stampabili
            'control_chars': re.compile(r'[\x00-\x1f\x7f-\x9f]'),
            # Caratteri Unicode pericolosi
            'unicode_dangerous': re.compile(r'[\u200b-\u200f\u202a-\u202e\u2060-\u206f]'),
        }

        # Caratteri permessi per diversi tipi di campo
        self.allowed_patterns = {
            'alphanumeric_extended': re.compile(r'^[A-Z0-9\s\-_+.,:()\/\\]*$'),
            'cable_id': re.compile(r'^[A-Z0-9\-_]*$'),
            'numeric_decimal': re.compile(r'^[0-9.,]*$'),
            'text_safe': re.compile(r'^[A-Z0-9\s\-_+.,:()\/\\àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]*$', re.IGNORECASE),
        }

        # Limiti di lunghezza per i campi
        self.field_limits = {
            'id_cavo': 50,
            'tipologia': 100,
            'sezione': 100,
            'utility': 50,
            'colore_cavo': 30,
            'sistema': 50,
            'ubicazione': 200,
            'utenza': 100,
            'descrizione': 500,
            'responsabile': 100,
            'comanda': 50,
            'default': 255
        }

        # Pattern per riconoscere diverse parti della sezione
        self.patterns = {
            # Pattern principale: cattura numero conduttori, separatore, sezione, unità, suffisso complesso
            'main': re.compile(
                r'^\s*\(?(\d+)\s*[xX×]\s*(\d+(?:[.,]\d+)?)\s*(mm2?|MM2?|mm²|MM²)?\s*(\+\s*(?:\d+(?:[.,]\d+)?(?:mm2?|MM2?|mm²|MM²)?)?[a-zA-Z0-9]*(?:\s*[a-zA-Z0-9]*)*)?.*?\)?\s*$',
                re.IGNORECASE
            ),
            
            # Pattern per sezioni senza conduttori (es: "240MM2")
            'simple': re.compile(
                r'^\s*\(?(\d+(?:[.,]\d+)?)\s*(mm2?|MM2?|mm²|MM²)?\s*(\+\s*(?:\d+(?:[.,]\d+)?(?:mm2?|MM2?|mm²|MM²)?)?[a-zA-Z0-9]*(?:\s*[a-zA-Z0-9]*)*)?.*?\)?\s*$',
                re.IGNORECASE
            ),
            
            # Pattern per suffissi complessi (es: "+2.5YG", "+SH")
            'suffix': re.compile(r'\+\s*(\d+(?:[.,]\d+)?(?:mm2?|MM2?|mm²|MM²)?)?([a-zA-Z0-9]+(?:\s*[a-zA-Z0-9]*)*)', re.IGNORECASE)
        }
        
        # Mappatura unità di misura standard
        self.unit_mapping = {
            'mm2': 'MM2',
            'mm²': 'MM2',
            'MM²': 'MM2',
            'MM2': 'MM2',
            '': 'MM2'  # Default se non specificato
        }
        
        # Mappatura suffissi standard
        self.suffix_mapping = {
            'sh': 'SH',
            'yg': 'YG',
            'ye': 'YE',
            'gn': 'GN',
            'pe': 'PE',
            'pvc': 'PVC'
        }

        # Dizionari di valori noti per correzione automatica
        self.known_tipologie = {
            'FG16OR16', 'FG7OR7', 'FG7OM1', 'FG7OM2', 'FG7OM3', 'FG7OM4',
            'LIYCY', 'LIYY', 'H07VK', 'H07VU', 'H05VK', 'H05VU',
            'UTP', 'FTP', 'SFTP', 'CAT5E', 'CAT6', 'CAT6A', 'CAT7',
            'RG58', 'RG59', 'RG6', 'RG11', 'RG213', 'RG214',
            'FROR', 'FG16OM1', 'FG16OM2', 'FG16OM3', 'FG16OM4',
            'N2XH', 'N2XSY', 'N2XSEY', 'N2XCH', 'N2XCHY',
            'YSLY', 'YSLCY', 'YY', 'YCY', 'OLFLEX'
        }

        self.known_colori = {
            'NERO', 'ROSSO', 'BLU', 'VERDE', 'GIALLO', 'BIANCO', 'GRIGIO',
            'MARRONE', 'ARANCIONE', 'VIOLA', 'ROSA', 'AZZURRO', 'LIME',
            'BLACK', 'RED', 'BLUE', 'GREEN', 'YELLOW', 'WHITE', 'GRAY',
            'BROWN', 'ORANGE', 'PURPLE', 'PINK', 'CYAN', 'MAGENTA'
        }

        self.known_utilities = {
            'ENEL', 'TIM', 'VODAFONE', 'WIND', 'FASTWEB', 'TISCALI',
            'TELECOM', 'ILIAD', 'LINKEM', 'OPEN FIBER', 'FLASH FIBER',
            'BT', 'COLT', 'RETELIT', 'SPARKLE', 'INTEROUTE'
        }

        # CORREZIONI AUTOMATICHE SICURE - SOLO errori di battitura evidenti
        # RIMOSSO tutto ciò che può causare allucinazioni o modifiche indesiderate
        self.auto_corrections = {
            # SOLO correzioni di caratteri speciali evidenti
            'FG1&OR16': 'FG16OR16',  # & invece di 6
            'FG16OR!6': 'FG16OR16',  # ! invece di 1

            # RIMOSSO: 'LICY': 'LIYCY' - causava allucinazioni
            # RIMOSSO: 'LYCY': 'LIYCY' - causava allucinazioni
            # RIMOSSO: tutte le altre correzioni aggressive

            # SOLO correzioni di duplicazioni evidenti
            'BLACKK': 'BLACK',
            'TIMM': 'TIM',
            'ENELL': 'ENEL',
            'VODAPHONE': 'VODAFONE',  # Solo se è un errore di spelling noto
        }

    def sanitize_input(self, value: str, field_type: str = 'default') -> str:
        """
        Sanitizza l'input rimuovendo caratteri pericolosi e normalizzando.

        Args:
            value: Valore da sanitizzare
            field_type: Tipo di campo per applicare regole specifiche

        Returns:
            Valore sanitizzato e sicuro
        """
        if not value or not isinstance(value, str):
            return ''

        # 1. Decodifica HTML entities
        value = html.unescape(value)

        # 2. Normalizza Unicode (rimuove accenti, converte caratteri speciali)
        value = unicodedata.normalize('NFKD', value)

        # 3. Rimuove caratteri pericolosi
        for pattern_name, pattern in self.dangerous_patterns.items():
            if pattern.search(value):
                self.logger.warning(f"Caratteri pericolosi rilevati ({pattern_name}): {value}")
                value = pattern.sub('', value)

        # 3.1. Rimuove tutti i tag HTML rimanenti
        value = re.sub(r'<[^>]*>', '', value)

        # 3.2. Rimuove caratteri di controllo e non stampabili
        value = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', value)

        # 3.3. Rimuove caratteri speciali pericolosi rimanenti
        value = re.sub(r'[!@#$%^&*()+=\[\]{}|\\:";\'<>?,./`~]', '', value)

        # 3.4. BOMBA-PROOF: Rimuove parole pericolose rimanenti
        dangerous_words = [
            'alert', 'eval', 'while', 'for', 'function', 'script', 'javascript',
            'drop', 'delete', 'insert', 'update', 'create', 'alter', 'exec',
            'union', 'select', 'from', 'where', 'table', 'database', 'admin',
            'password', 'user', 'login', 'auth', 'cookie', 'document'
        ]

        for word in dangerous_words:
            value = re.sub(re.escape(word), '', value, flags=re.IGNORECASE)

        # 3.5. BOMBA-PROOF: Rimuove sequenze ripetitive sospette
        value = re.sub(r'(.)\1{10,}', r'\1', value)  # Max 10 caratteri ripetuti

        # 3.6. BOMBA-PROOF: Rimuove parentesi eccessive
        value = re.sub(r'[()]{5,}', '', value)  # Max 4 parentesi consecutive

        # 4. Rimuove spazi multipli e normalizza
        value = re.sub(r'\s+', ' ', value).strip()

        # 5. Applica limiti di lunghezza
        max_length = self.field_limits.get(field_type, self.field_limits['default'])
        if len(value) > max_length:
            self.logger.warning(f"Campo {field_type} troncato da {len(value)} a {max_length} caratteri")
            value = value[:max_length].strip()

        return value

    def calculate_similarity(self, str1: str, str2: str) -> float:
        """
        Calcola la similarità tra due stringhe usando SequenceMatcher.

        Args:
            str1: Prima stringa
            str2: Seconda stringa

        Returns:
            Valore di similarità tra 0.0 e 1.0
        """
        return SequenceMatcher(None, str1.upper(), str2.upper()).ratio()

    def find_best_match(self, input_value: str, known_values: Set[str], threshold: float = 0.6) -> Optional[str]:
        """
        Trova la migliore corrispondenza per un valore di input.

        Args:
            input_value: Valore da correggere
            known_values: Set di valori noti
            threshold: Soglia minima di similarità

        Returns:
            Migliore corrispondenza trovata o None
        """
        if not input_value or not known_values:
            return None

        input_clean = input_value.upper().strip()

        # Controllo esatto
        if input_clean in known_values:
            return input_clean

        best_match = None
        best_score = 0.0

        for known_value in known_values:
            score = self.calculate_similarity(input_clean, known_value)
            if score > best_score and score >= threshold:
                best_score = score
                best_match = known_value

        return best_match

    def auto_correct_value(self, value: str, field_type: str) -> str:
        """
        CORREZIONE CRITICA: Applica SOLO correzioni automatiche dirette e sicure.
        NON usa più la similarità per evitare allucinazioni e modifiche indesiderate.

        Args:
            value: Valore da correggere
            field_type: Tipo di campo

        Returns:
            Valore corretto SOLO se c'è una corrispondenza esatta nel dizionario
        """
        if not value or not isinstance(value, str):
            return ''

        value_clean = value.upper().strip()

        # SOLO correzioni automatiche dirette e sicure
        if value_clean in self.auto_corrections:
            corrected = self.auto_corrections[value_clean]
            self.logger.info(f"Auto-correzione diretta sicura: '{value}' → '{corrected}'")
            return corrected

        # RIMOSSA la correzione basata su similarità per evitare allucinazioni
        # La similarità causava modifiche indesiderate come LYICY → FROR

        # Restituisce il valore originale pulito senza modifiche sostanziali
        return value_clean

    def normalize_text_field(self, value: str, field_type: str = 'default', uppercase: bool = True) -> str:
        """
        CORREZIONE CRITICA: Normalizza un campo di testo SENZA modifiche sostanziali.
        SOLO pulizia base e standardizzazione sicura.

        Args:
            value: Valore da normalizzare
            field_type: Tipo di campo
            uppercase: Se convertire in maiuscolo

        Returns:
            Valore normalizzato SENZA perdita di dati
        """
        if not value or not isinstance(value, str):
            return ''

        # SOLO pulizia base: rimuove spazi extra
        value = re.sub(r'\s+', ' ', value).strip()

        # SOLO correzioni automatiche sicure (caratteri speciali evidenti)
        value = self.auto_corrections.get(value.upper(), value)

        # Converte in maiuscolo se richiesto
        if uppercase:
            value = value.upper()

        # RIMOSSA la sanitizzazione aggressiva che poteva alterare i dati
        # RIMOSSA la correzione automatica basata su similarità

        return value

    def normalize_cable_id(self, value: str) -> str:
        """Normalizza un ID cavo."""
        if not value or not isinstance(value, str):
            return ''

        # Sanitizza e converte in maiuscolo
        value = self.sanitize_input(value, 'id_cavo')
        value = value.upper()

        # Rimuove spazi e caratteri non permessi
        value = re.sub(r'[^A-Z0-9\-_]', '', value)

        return value
    
    def normalize_decimal(self, value: str) -> str:
        """Normalizza i separatori decimali."""
        # Sostituisce virgola con punto
        normalized = value.replace(',', '.')
        
        # Rimuove zeri trailing dopo il punto decimale
        if '.' in normalized:
            normalized = normalized.rstrip('0').rstrip('.')
        
        return normalized
    
    def normalize_unit(self, unit: str) -> str:
        """Normalizza le unità di misura."""
        if not unit:
            return 'MM2'
        
        unit_clean = unit.strip().lower()
        return self.unit_mapping.get(unit_clean, 'MM2')
    
    def normalize_suffix(self, suffix: str) -> str:
        """Normalizza i suffissi (+SH, +YG, +2.5YG, etc.)."""
        if not suffix:
            return ''
        
        # Usa il pattern per estrarre numero e lettere
        match = self.patterns['suffix'].match(suffix)
        if match:
            number_part = match.group(1) or ''
            letter_part = match.group(2) or ''
            
            # Normalizza la parte numerica se presente
            if number_part:
                number_part = self.normalize_decimal(number_part.replace('mm2', '').replace('MM2', '').replace('mm²', '').replace('MM²', ''))
            
            # Normalizza la parte letterale
            letter_normalized = self.suffix_mapping.get(letter_part.lower(), letter_part.upper())
            
            return f"+{number_part}{letter_normalized}" if number_part or letter_normalized else ''
        
        # Fallback: rimuove il + iniziale e spazi, normalizza
        suffix_clean = suffix.replace('+', '').strip()
        parts = []
        for part in suffix_clean.split():
            part_lower = part.lower()
            normalized_part = self.suffix_mapping.get(part_lower, part.upper())
            parts.append(normalized_part)
        
        return '+' + ''.join(parts) if parts else ''
    
    def normalize_section(self, section: str) -> str:
        """
        CORREZIONE CRITICA: Normalizza una sezione di cavo SENZA perdere dati.
        SOLO pulizia e standardizzazione, MAI modifica del contenuto sostanziale.

        Args:
            section: Sezione da normalizzare (es: "3x2.5 mm2", "4x1,5+sh")

        Returns:
            Sezione normalizzata (es: "3X2.5MM2", "4X1.5+SH") PRESERVANDO tutti i dati
        """
        if not section or not isinstance(section, str):
            return ''

        # SOLO pulizia base: rimuove spazi extra all'inizio/fine
        section_clean = section.strip()

        # CORREZIONE CRITICA: Se non riusciamo a parsare correttamente,
        # restituiamo la stringa originale pulita SENZA perdere dati

        # Prova prima il pattern principale (con conduttori)
        match = self.patterns['main'].match(section_clean)

        if match:
            conductors = match.group(1)
            wire_section = match.group(2)
            unit = match.group(3) or ''
            suffix = match.group(4) or ''

            # VERIFICA CRITICA: Assicurati che tutti i componenti siano presenti
            if not conductors or not wire_section:
                self.logger.warning(f"Pattern match incompleto per '{section}', usando fallback sicuro")
                # Fallback sicuro: solo pulizia base
                return section_clean.upper().replace(' ', '').replace(',', '.')

            # Normalizza ogni componente SENZA perdere dati
            normalized_section = self.normalize_decimal(wire_section)
            normalized_unit = self.normalize_unit(unit) if unit else 'MM2'
            normalized_suffix = self.normalize_suffix(suffix) if suffix else ''

            # Costruisce la sezione normalizzata PRESERVANDO tutti i dati
            result = f"{conductors}X{normalized_section}{normalized_unit}{normalized_suffix}"

            self.logger.debug(f"Normalized '{section}' -> '{result}' (main pattern)")
            return result

        # Prova il pattern semplice (senza conduttori)
        match = self.patterns['simple'].match(section_clean)

        if match:
            wire_section = match.group(1)
            unit = match.group(2) or ''
            suffix = match.group(3) or ''

            # VERIFICA CRITICA: Assicurati che la sezione sia presente
            if not wire_section:
                self.logger.warning(f"Pattern simple incompleto per '{section}', usando fallback sicuro")
                # Fallback sicuro: solo pulizia base
                return section_clean.upper().replace(' ', '').replace(',', '.')

            # Normalizza ogni componente SENZA perdere dati
            normalized_section = self.normalize_decimal(wire_section)
            normalized_unit = self.normalize_unit(unit) if unit else 'MM2'
            normalized_suffix = self.normalize_suffix(suffix) if suffix else ''

            # Costruisce la sezione normalizzata PRESERVANDO tutti i dati
            result = f"{normalized_section}{normalized_unit}{normalized_suffix}"

            self.logger.debug(f"Normalized '{section}' -> '{result}' (simple pattern)")
            return result

        # FALLBACK SICURO: Se non matcha nessun pattern, restituisce la stringa originale
        # con SOLO pulizia base (spazi, virgole) SENZA perdere dati sostanziali
        result = section_clean.upper().replace(' ', '').replace(',', '.')
        self.logger.warning(f"Could not parse '{section}', using safe fallback: '{result}'")
        return result
    
    def normalize_tipologia(self, tipologia: str) -> str:
        """
        CORREZIONE CRITICA: Normalizza la tipologia SENZA modifiche sostanziali.
        SOLO pulizia e correzioni sicure, MAI allucinazioni.
        """
        if not tipologia or not isinstance(tipologia, str):
            return ''

        # SOLO pulizia base: rimuove spazi extra e converte in maiuscolo
        tipologia_clean = tipologia.strip().upper()

        # SOLO correzioni automatiche sicure (caratteri speciali evidenti)
        tipologia_corrected = self.auto_corrections.get(tipologia_clean, tipologia_clean)

        # RIMOSSA la sanitizzazione aggressiva che poteva alterare i dati
        # RIMOSSA la correzione automatica basata su similarità

        return tipologia_corrected
    
    def normalize_cable_data(self, tipologia: str, sezione: str) -> Tuple[str, str]:
        """
        Normalizza sia tipologia che sezione.

        Args:
            tipologia: Tipologia del cavo
            sezione: Sezione del cavo

        Returns:
            Tupla (tipologia_normalizzata, sezione_normalizzata)
        """
        normalized_tipologia = self.normalize_tipologia(tipologia)
        normalized_sezione = self.normalize_section(sezione)

        return normalized_tipologia, normalized_sezione

    def normalize_all_cable_fields(self, cable_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalizza tutti i campi di un cavo per sicurezza e consistenza.

        Args:
            cable_data: Dizionario con i dati del cavo

        Returns:
            Dizionario con tutti i campi normalizzati
        """
        normalized = {}

        # Campi che richiedono normalizzazione specifica
        field_normalizers = {
            'id_cavo': lambda x: self.normalize_cable_id(x),
            'tipologia': lambda x: self.normalize_tipologia(x),
            'sezione': lambda x: self.normalize_section(x),
            'utility': lambda x: self.normalize_text_field(x, 'utility', uppercase=True),
            'colore_cavo': lambda x: self.normalize_text_field(x, 'colore_cavo', uppercase=True),
            'sistema': lambda x: self.normalize_text_field(x, 'sistema', uppercase=True),
            'ubicazione_partenza': lambda x: self.normalize_text_field(x, 'ubicazione', uppercase=True),
            'ubicazione_arrivo': lambda x: self.normalize_text_field(x, 'ubicazione', uppercase=True),
            'utenza_partenza': lambda x: self.normalize_text_field(x, 'utenza', uppercase=True),
            'utenza_arrivo': lambda x: self.normalize_text_field(x, 'utenza', uppercase=True),
            'descrizione_utenza_partenza': lambda x: self.normalize_text_field(x, 'descrizione', uppercase=False),
            'descrizione_utenza_arrivo': lambda x: self.normalize_text_field(x, 'descrizione', uppercase=False),
            'responsabile_posa': lambda x: self.normalize_text_field(x, 'responsabile', uppercase=True),
            'responsabile_partenza': lambda x: self.normalize_text_field(x, 'responsabile', uppercase=True),
            'responsabile_arrivo': lambda x: self.normalize_text_field(x, 'responsabile', uppercase=True),
            'comanda_posa': lambda x: self.normalize_text_field(x, 'comanda', uppercase=True),
            'comanda_partenza': lambda x: self.normalize_text_field(x, 'comanda', uppercase=True),
            'comanda_arrivo': lambda x: self.normalize_text_field(x, 'comanda', uppercase=True),
            'revisione_ufficiale': lambda x: self.normalize_text_field(x, 'default', uppercase=True),
            'stato_installazione': lambda x: self.normalize_text_field(x, 'default', uppercase=True),
            'id_bobina': lambda x: self.normalize_text_field(x, 'default', uppercase=True),
        }

        # Campi numerici che non richiedono normalizzazione speciale
        numeric_fields = {'metri_teorici', 'metratura_reale', 'n_conduttori', 'sh', 'collegamenti', 'modificato_manualmente'}

        # Campi di sistema che non devono essere modificati
        system_fields = {'id_cantiere', 'timestamp', 'data_posa'}

        # Normalizza ogni campo
        for field, value in cable_data.items():
            if field in system_fields:
                # Campi di sistema: copia senza modifiche
                normalized[field] = value
            elif field in field_normalizers:
                # Campi con normalizzazione specifica
                if value is not None:
                    try:
                        normalized[field] = field_normalizers[field](str(value))
                    except Exception as e:
                        self.logger.error(f"Errore normalizzazione campo {field}: {e}")
                        normalized[field] = self.sanitize_input(str(value) if value is not None else '')
                else:
                    normalized[field] = None
            elif field in numeric_fields:
                # Campi numerici: sanitizza solo se stringa
                if isinstance(value, str):
                    normalized[field] = self.sanitize_input(value, 'numeric_decimal')
                else:
                    normalized[field] = value
            else:
                # Altri campi: normalizzazione generica
                if isinstance(value, str):
                    normalized[field] = self.normalize_text_field(value, 'default', uppercase=True)
                else:
                    normalized[field] = value

        return normalized

    def update_known_values_from_db(self, db_connection=None):
        """
        Aggiorna i dizionari di valori noti dal database.

        Args:
            db_connection: Connessione al database (opzionale)
        """
        if not db_connection:
            try:
                from backend.modules.database_pg import Database
                db = Database()
                conn = db.get_dict_cursor_connection()
            except ImportError:
                self.logger.warning("Impossibile importare Database, usando valori predefiniti")
                return
        else:
            conn = db_connection

        try:
            with conn.cursor() as cur:
                # Aggiorna tipologie dal database
                cur.execute("""
                    SELECT DISTINCT tipologia
                    FROM cavi
                    WHERE tipologia IS NOT NULL
                      AND tipologia != ''
                    UNION
                    SELECT DISTINCT tipologia
                    FROM parco_cavi
                    WHERE tipologia IS NOT NULL
                      AND tipologia != ''
                """)

                db_tipologie = {row['tipologia'].upper().strip() for row in cur.fetchall() if row['tipologia']}
                self.known_tipologie.update(db_tipologie)

                # Aggiorna colori dal database
                cur.execute("""
                    SELECT DISTINCT colore_cavo
                    FROM cavi
                    WHERE colore_cavo IS NOT NULL
                      AND colore_cavo != ''
                """)

                db_colori = {row['colore_cavo'].upper().strip() for row in cur.fetchall() if row['colore_cavo']}
                self.known_colori.update(db_colori)

                # Aggiorna utilities dal database
                cur.execute("""
                    SELECT DISTINCT utility
                    FROM cavi
                    WHERE utility IS NOT NULL
                      AND utility != ''
                """)

                db_utilities = {row['utility'].upper().strip() for row in cur.fetchall() if row['utility']}
                self.known_utilities.update(db_utilities)

                self.logger.info(f"Aggiornati dizionari: {len(self.known_tipologie)} tipologie, {len(self.known_colori)} colori, {len(self.known_utilities)} utilities")

        except Exception as e:
            self.logger.error(f"Errore aggiornamento dizionari dal database: {e}")
        finally:
            if not db_connection:  # Chiudi solo se abbiamo creato noi la connessione
                conn.close()

# Istanza globale del normalizzatore
_normalizer = CableNormalizer()

# Funzioni di utilità per l'integrazione nel sistema
def normalize_cable_section(section: str) -> str:
    """Funzione di utilità per normalizzare una sezione."""
    return _normalizer.normalize_section(section)

def normalize_cable_type(tipologia: str) -> str:
    """Funzione di utilità per normalizzare una tipologia."""
    return _normalizer.normalize_tipologia(tipologia)

def normalize_cable_data(tipologia: str, sezione: str) -> Tuple[str, str]:
    """Funzione di utilità per normalizzare tipologia e sezione insieme."""
    return _normalizer.normalize_cable_data(tipologia, sezione)

def normalize_all_cable_fields(cable_data: Dict[str, Any]) -> Dict[str, Any]:
    """Funzione di utilità per normalizzare tutti i campi di un cavo."""
    return _normalizer.normalize_all_cable_fields(cable_data)

def sanitize_input(value: str, field_type: str = 'default') -> str:
    """Funzione di utilità per sanitizzare un input."""
    return _normalizer.sanitize_input(value, field_type)

def normalize_text_field(value: str, field_type: str = 'default', uppercase: bool = True) -> str:
    """Funzione di utilità per normalizzare un campo di testo."""
    return _normalizer.normalize_text_field(value, field_type, uppercase)

def auto_correct_value(value: str, field_type: str) -> str:
    """Funzione di utilità per correggere automaticamente un valore."""
    return _normalizer.auto_correct_value(value, field_type)

def update_known_values_from_db(db_connection=None):
    """Funzione di utilità per aggiornare i dizionari dal database."""
    return _normalizer.update_known_values_from_db(db_connection)
