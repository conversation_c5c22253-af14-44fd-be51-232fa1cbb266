[{"C:\\CMS\\webapp\\frontend\\src\\index.js": "1", "C:\\CMS\\webapp\\frontend\\src\\App.js": "2", "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js": "3", "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js": "4", "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js": "5", "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js": "6", "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js": "7", "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js": "8", "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js": "9", "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js": "10", "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js": "11", "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js": "12", "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js": "13", "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js": "14", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js": "15", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js": "16", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js": "17", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js": "18", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js": "19", "C:\\CMS\\webapp\\frontend\\src\\config.js": "20", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js": "21", "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js": "22", "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx": "23", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js": "24", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js": "25", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js": "26", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js": "27", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js": "28", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js": "29", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js": "30", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js": "31", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js": "32", "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js": "33", "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js": "34", "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js": "35", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js": "36", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js": "37", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js": "38", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js": "39", "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js": "40", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js": "41", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js": "42", "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js": "43", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js": "44", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js": "45", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js": "46", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js": "47", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js": "48", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js": "49", "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js": "50", "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js": "51", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js": "52", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js": "53", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js": "54", "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js": "55", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js": "56", "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js": "57", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js": "58", "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js": "59", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx": "60", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx": "61", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx": "62", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx": "63", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js": "64", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js": "65", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js": "66", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js": "67", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js": "68", "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js": "69", "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js": "70", "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js": "71", "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js": "72", "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js": "73", "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js": "74", "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js": "75", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js": "76", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js": "77", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js": "78", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js": "79", "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js": "80", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js": "81", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js": "82", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js": "83", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js": "84", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js": "85", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js": "86", "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js": "87", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js": "88", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js": "89", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js": "90", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js": "91", "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js": "92", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js": "93", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js": "94", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js": "95", "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCEI64_8Page.js": "96", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\CertificazioneCEI64_8.js": "97", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js": "98", "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js": "99", "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js": "100", "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js": "101", "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js": "102", "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js": "103", "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js": "104", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js": "105", "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js": "106", "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js": "107", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js": "108", "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js": "109"}, {"size": 557, "mtime": 1746952718482, "results": "110", "hashOfConfig": "111"}, {"size": 3196, "mtime": 1748982170834, "results": "112", "hashOfConfig": "111"}, {"size": 996, "mtime": 1746970152489, "results": "113", "hashOfConfig": "111"}, {"size": 10788, "mtime": 1746864244183, "results": "114", "hashOfConfig": "111"}, {"size": 21191, "mtime": 1748751093271, "results": "115", "hashOfConfig": "111"}, {"size": 6706, "mtime": 1749067060315, "results": "116", "hashOfConfig": "111"}, {"size": 2216, "mtime": 1746640055487, "results": "117", "hashOfConfig": "111"}, {"size": 7394, "mtime": 1748034003517, "results": "118", "hashOfConfig": "111"}, {"size": 6749, "mtime": 1746282201800, "results": "119", "hashOfConfig": "111"}, {"size": 17794, "mtime": 1749066780676, "results": "120", "hashOfConfig": "111"}, {"size": 2535, "mtime": 1746647873596, "results": "121", "hashOfConfig": "111"}, {"size": 2050, "mtime": 1746647945415, "results": "122", "hashOfConfig": "111"}, {"size": 700, "mtime": 1747545501078, "results": "123", "hashOfConfig": "111"}, {"size": 17518, "mtime": 1748664526035, "results": "124", "hashOfConfig": "111"}, {"size": 3028, "mtime": 1748816305304, "results": "125", "hashOfConfig": "111"}, {"size": 2070, "mtime": 1748815989656, "results": "126", "hashOfConfig": "111"}, {"size": 1630, "mtime": 1746336079554, "results": "127", "hashOfConfig": "111"}, {"size": 1909, "mtime": 1748722592098, "results": "128", "hashOfConfig": "111"}, {"size": 61136, "mtime": 1749066566770, "results": "129", "hashOfConfig": "111"}, {"size": 324, "mtime": 1748757444974, "results": "130", "hashOfConfig": "111"}, {"size": 9068, "mtime": 1746856425683, "results": "131", "hashOfConfig": "111"}, {"size": 2210, "mtime": 1747432283057, "results": "132", "hashOfConfig": "111"}, {"size": 4494, "mtime": 1748121063631, "results": "133", "hashOfConfig": "111"}, {"size": 38195, "mtime": 1748813903832, "results": "134", "hashOfConfig": "111"}, {"size": 3337, "mtime": 1748816346924, "results": "135", "hashOfConfig": "111"}, {"size": 2958, "mtime": 1748816316425, "results": "136", "hashOfConfig": "111"}, {"size": 3507, "mtime": 1748816326922, "results": "137", "hashOfConfig": "111"}, {"size": 3345, "mtime": 1748816357091, "results": "138", "hashOfConfig": "111"}, {"size": 3340, "mtime": 1748816336281, "results": "139", "hashOfConfig": "111"}, {"size": 2975, "mtime": 1747554796402, "results": "140", "hashOfConfig": "111"}, {"size": 3429, "mtime": 1747721794176, "results": "141", "hashOfConfig": "111"}, {"size": 2929, "mtime": 1747655572696, "results": "142", "hashOfConfig": "111"}, {"size": 6125, "mtime": 1748705680231, "results": "143", "hashOfConfig": "111"}, {"size": 5880, "mtime": 1748121404574, "results": "144", "hashOfConfig": "111"}, {"size": 3889, "mtime": 1748664890350, "results": "145", "hashOfConfig": "111"}, {"size": 4720, "mtime": 1746771178920, "results": "146", "hashOfConfig": "111"}, {"size": 7121, "mtime": 1746281148395, "results": "147", "hashOfConfig": "111"}, {"size": 7958, "mtime": 1746280443400, "results": "148", "hashOfConfig": "111"}, {"size": 6259, "mtime": 1746965906057, "results": "149", "hashOfConfig": "111"}, {"size": 4215, "mtime": 1746278746358, "results": "150", "hashOfConfig": "111"}, {"size": 1273, "mtime": 1746809069006, "results": "151", "hashOfConfig": "111"}, {"size": 14270, "mtime": 1748371983481, "results": "152", "hashOfConfig": "111"}, {"size": 2752, "mtime": 1747022186740, "results": "153", "hashOfConfig": "111"}, {"size": 1072, "mtime": 1746637929350, "results": "154", "hashOfConfig": "111"}, {"size": 6745, "mtime": 1747545492454, "results": "155", "hashOfConfig": "111"}, {"size": 41680, "mtime": 1748816669877, "results": "156", "hashOfConfig": "111"}, {"size": 500, "mtime": 1748722841235, "results": "157", "hashOfConfig": "111"}, {"size": 47844, "mtime": 1748876421138, "results": "158", "hashOfConfig": "111"}, {"size": 45871, "mtime": 1749067127143, "results": "159", "hashOfConfig": "111"}, {"size": 1947, "mtime": 1748120984640, "results": "160", "hashOfConfig": "111"}, {"size": 54895, "mtime": 1748370360136, "results": "161", "hashOfConfig": "111"}, {"size": 14635, "mtime": 1748666301849, "results": "162", "hashOfConfig": "111"}, {"size": 15230, "mtime": 1748984833955, "results": "163", "hashOfConfig": "111"}, {"size": 11835, "mtime": 1748920731807, "results": "164", "hashOfConfig": "111"}, {"size": 2211, "mtime": 1748686293878, "results": "165", "hashOfConfig": "111"}, {"size": 9215, "mtime": 1748668814050, "results": "166", "hashOfConfig": "111"}, {"size": 10993, "mtime": 1747154871546, "results": "167", "hashOfConfig": "111"}, {"size": 12150, "mtime": 1748205557322, "results": "168", "hashOfConfig": "111"}, {"size": 24566, "mtime": 1748691444876, "results": "169", "hashOfConfig": "111"}, {"size": 7032, "mtime": 1748069273238, "results": "170", "hashOfConfig": "111"}, {"size": 8589, "mtime": 1748207111023, "results": "171", "hashOfConfig": "111"}, {"size": 9979, "mtime": 1748069243848, "results": "172", "hashOfConfig": "111"}, {"size": 10821, "mtime": 1748069202177, "results": "173", "hashOfConfig": "111"}, {"size": 36555, "mtime": 1747684003188, "results": "174", "hashOfConfig": "111"}, {"size": 9483, "mtime": 1747194869458, "results": "175", "hashOfConfig": "111"}, {"size": 20387, "mtime": 1748984521895, "results": "176", "hashOfConfig": "111"}, {"size": 48588, "mtime": 1747948123233, "results": "177", "hashOfConfig": "111"}, {"size": 92270, "mtime": 1748123070273, "results": "178", "hashOfConfig": "111"}, {"size": 522, "mtime": 1747022186711, "results": "179", "hashOfConfig": "111"}, {"size": 10251, "mtime": 1748805459799, "results": "180", "hashOfConfig": "111"}, {"size": 7740, "mtime": 1748881233022, "results": "181", "hashOfConfig": "111"}, {"size": 1703, "mtime": 1746972529152, "results": "182", "hashOfConfig": "111"}, {"size": 19892, "mtime": 1747554544219, "results": "183", "hashOfConfig": "111"}, {"size": 12050, "mtime": 1747547543421, "results": "184", "hashOfConfig": "111"}, {"size": 1686, "mtime": 1746946499500, "results": "185", "hashOfConfig": "111"}, {"size": 5145, "mtime": 1746914029633, "results": "186", "hashOfConfig": "111"}, {"size": 10721, "mtime": 1748751269815, "results": "187", "hashOfConfig": "111"}, {"size": 22179, "mtime": 1747432554979, "results": "188", "hashOfConfig": "111"}, {"size": 2574, "mtime": 1748920719208, "results": "189", "hashOfConfig": "111"}, {"size": 4094, "mtime": 1748161663641, "results": "190", "hashOfConfig": "111"}, {"size": 5273, "mtime": 1747946737459, "results": "191", "hashOfConfig": "111"}, {"size": 4346, "mtime": 1747491472989, "results": "192", "hashOfConfig": "111"}, {"size": 15647, "mtime": 1748899398456, "results": "193", "hashOfConfig": "111"}, {"size": 6742, "mtime": 1748751174061, "results": "194", "hashOfConfig": "111"}, {"size": 6529, "mtime": 1748664406267, "results": "195", "hashOfConfig": "111"}, {"size": 15764, "mtime": 1748877145346, "results": "196", "hashOfConfig": "111"}, {"size": 6899, "mtime": 1748877131332, "results": "197", "hashOfConfig": "111"}, {"size": 5536, "mtime": 1748670096009, "results": "198", "hashOfConfig": "111"}, {"size": 5457, "mtime": 1748666884369, "results": "199", "hashOfConfig": "111"}, {"size": 5605, "mtime": 1748666925194, "results": "200", "hashOfConfig": "111"}, {"size": 77752, "mtime": 1748878387989, "results": "201", "hashOfConfig": "111"}, {"size": 2807, "mtime": 1748705699971, "results": "202", "hashOfConfig": "111"}, {"size": 23591, "mtime": 1748881382254, "results": "203", "hashOfConfig": "111"}, {"size": 3708, "mtime": 1748705727900, "results": "204", "hashOfConfig": "111"}, {"size": 10270, "mtime": 1748724524628, "results": "205", "hashOfConfig": "111"}, {"size": 8247, "mtime": 1748756088995, "results": "206", "hashOfConfig": "111"}, {"size": 11038, "mtime": 1748756003708, "results": "207", "hashOfConfig": "111"}, {"size": 15055, "mtime": 1748755908778, "results": "208", "hashOfConfig": "111"}, {"size": 16415, "mtime": 1748755956687, "results": "209", "hashOfConfig": "111"}, {"size": 3434, "mtime": 1748755857115, "results": "210", "hashOfConfig": "111"}, {"size": 3483, "mtime": 1748755829302, "results": "211", "hashOfConfig": "111"}, {"size": 3508, "mtime": 1748755842942, "results": "212", "hashOfConfig": "111"}, {"size": 956, "mtime": 1748878396989, "results": "213", "hashOfConfig": "111"}, {"size": 13327, "mtime": 1748881322351, "results": "214", "hashOfConfig": "111"}, {"size": 16151, "mtime": 1748981113532, "results": "215", "hashOfConfig": "111"}, {"size": 3613, "mtime": 1748921268108, "results": "216", "hashOfConfig": "111"}, {"size": 1153, "mtime": 1748921279608, "results": "217", "hashOfConfig": "111"}, {"size": 6579, "mtime": 1748922219011, "results": "218", "hashOfConfig": "111"}, {"size": 8976, "mtime": 1748922249445, "results": "219", "hashOfConfig": "111"}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0jzw9", {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\CMS\\webapp\\frontend\\src\\index.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\App.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\GlobalContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\context\\AuthContext.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\LoginPageNew.js", ["547"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\Dashboard.js", ["548", "549", "550", "551", "552"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\authService.js", ["553", "554", "555", "556"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\AdminPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\TopNavbar.js", ["557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CaviPage.js", ["569"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\HomePage.js", ["570", "571", "572", "573", "574", "575", "576", "577", "578", "579"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\TestBobinePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\UserPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ParcoCaviPage.js", ["580", "581", "582", "583", "584", "585", "586", "587", "588", "589", "590", "591", "592", "593", "594"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCaviPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserExpirationChecker.js", ["595"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\GestioneComandeePage.js", ["596", "597", "598", "599", "600", "601", "602", "603"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\VisualizzaCaviPage.js", ["604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624"], [], "C:\\CMS\\webapp\\frontend\\src\\config.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\TestCaviPage.js", ["625"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\axiosConfig.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\CertificazioniPageDebug.jsx", ["626"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\ReportCaviPageNew.js", ["627", "628", "629"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\EliminaBobinaPage.js", ["630", "631", "632", "633"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\VisualizzaBobinePage.js", ["634", "635"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\CreaBobinaPage.js", ["636", "637"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\StoricoUtilizzoPage.js", ["638", "639", "640", "641"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\parco\\ModificaBobinaPage.js", ["642", "643", "644", "645"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\MetriPosatiSemplificatoPage.js", ["646"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\ModificaBobinaPage.js", ["647", "648", "649", "650", "651", "652", "653"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\posa\\InserisciMetriPage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cantieri\\CantierePage.js", ["654", "655"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\excelService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\cantieriService.js", ["656", "657"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ImpersonateUser.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UsersList.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\UserForm.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\DatabaseView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\admin\\ResetDatabase.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\SelectedCantiereDisplay.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ExcelPopup.js", ["658", "659"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\userService.js", ["660", "661"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\AdminHomeButton.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\TestBobineComponent.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCavi.js", ["662", "663", "664", "665", "666", "667", "668", "669"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\GestioneComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ParcoCavi.js", ["670", "671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\PosaCaviCollegamenti.js", ["684", "685", "686", "687", "688", "689", "690"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\apiService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\caviService.js", ["691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoForm.js", ["714", "715"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CaviFilterableTable.js", ["716", "717", "718", "719", "720", "721", "722", "723"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\reportService.js", ["724", "725"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\ProgressChart.js", ["726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\validationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\TimelineChart.js", ["737"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\charts\\BoqChart.js", ["738", "739", "740", "741", "742", "743", "744", "745", "746", "747", "748", "749", "750", "751", "752", "753", "754", "755", "756", "757", "758", "759"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentiList.jsx", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\StrumentoForm.jsx", ["760"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioneForm.jsx", ["761"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazioni\\CertificazioniList.jsx", ["762"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\MetriPosatiSemplificatoForm.js", ["763", "764", "765", "766", "767", "768", "769", "770", "771", "772", "773"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SelezionaCavoForm.js", ["774"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CollegamentiCavo.js", ["775", "776", "777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaForm.js", ["789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriForm.js", ["813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\adminService.js", ["829", "830"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\certificazioneService.js", ["831"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\comandeService.js", ["832", "833"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\navigationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\parcoCaviService.js", ["834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853"], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\bobinaValidationUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\dateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ConfigurazioneDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\BobineFilterableTable.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\QuickAddCablesDialog.js", ["854", "855", "856", "857", "858", "859", "860", "861"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\FilterableTableHeader.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\utils\\stateUtils.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\IncompatibleReelDialog.js", ["862"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CavoDetailsView.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ExcelLikeFilter.js", ["863", "864", "865"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\CantieriFilterableTable.js", ["866", "867"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\EditCantiereDialog.js", ["868"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\PasswordManagementDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cantieri\\HoldToViewButton.js", ["869"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\MetricCard.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\EmptyState.js", ["870"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ReportSection.js", ["871"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\CertificazioneCaviImproved.js", ["872", "873", "874", "875", "876", "877"], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\comande\\ComandePage.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\ComandeList.js", ["878"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\TestComande.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\AccessoRapidoComanda.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\pages\\cavi\\CertificazioneCEI64_8Page.js", ["879", "880", "881", "882", "883", "884", "885", "886"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\CertificazioneCEI64_8.js", ["887", "888", "889", "890", "891", "892", "893"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\RapportiGenerali.js", ["894", "895", "896", "897", "898", "899", "900", "901", "902"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\certificazione\\ProveDettagliate.js", ["903", "904", "905", "906", "907", "908", "909", "910", "911"], [], "C:\\CMS\\webapp\\frontend\\src\\services\\nonConformitaService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\rapportiGeneraliService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\services\\proveDettagliateService.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\Logo.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\comande\\CreaComandaConCavi.js", ["912", "913", "914"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\SmartCaviFilter.js", ["915", "916", "917"], [], "C:\\CMS\\webapp\\frontend\\src\\components\\common\\ContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\hooks\\useContextMenu.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\InserisciMetriDialog.js", [], [], "C:\\CMS\\webapp\\frontend\\src\\components\\cavi\\ModificaBobinaDialog.js", ["918", "919", "920", "921"], [], {"ruleId": "922", "severity": 1, "message": "923", "line": 12, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 12, "endColumn": 14}, {"ruleId": "926", "severity": 1, "message": "927", "line": 94, "column": 71, "nodeType": "928", "messageId": "929", "endLine": 94, "endColumn": 100}, {"ruleId": "926", "severity": 1, "message": "927", "line": 95, "column": 70, "nodeType": "928", "messageId": "929", "endLine": 95, "endColumn": 99}, {"ruleId": "926", "severity": 1, "message": "927", "line": 96, "column": 67, "nodeType": "928", "messageId": "929", "endLine": 96, "endColumn": 96}, {"ruleId": "926", "severity": 1, "message": "927", "line": 97, "column": 76, "nodeType": "928", "messageId": "929", "endLine": 97, "endColumn": 105}, {"ruleId": "926", "severity": 1, "message": "927", "line": 98, "column": 71, "nodeType": "928", "messageId": "929", "endLine": 98, "endColumn": 100}, {"ruleId": "930", "severity": 1, "message": "931", "line": 78, "column": 11, "nodeType": "932", "messageId": "933", "endLine": 78, "endColumn": 115}, {"ruleId": "930", "severity": 1, "message": "931", "line": 80, "column": 11, "nodeType": "932", "messageId": "933", "endLine": 80, "endColumn": 107}, {"ruleId": "930", "severity": 1, "message": "931", "line": 86, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 86, "endColumn": 105}, {"ruleId": "930", "severity": 1, "message": "931", "line": 89, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 89, "endColumn": 41}, {"ruleId": "922", "severity": 1, "message": "934", "line": 13, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 13, "endColumn": 9}, {"ruleId": "922", "severity": 1, "message": "935", "line": 20, "column": 25, "nodeType": "924", "messageId": "925", "endLine": 20, "endColumn": 34}, {"ruleId": "922", "severity": 1, "message": "936", "line": 21, "column": 19, "nodeType": "924", "messageId": "925", "endLine": 21, "endColumn": 35}, {"ruleId": "922", "severity": 1, "message": "937", "line": 22, "column": 12, "nodeType": "924", "messageId": "925", "endLine": 22, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "938", "line": 23, "column": 18, "nodeType": "924", "messageId": "925", "endLine": 23, "endColumn": 28}, {"ruleId": "922", "severity": 1, "message": "939", "line": 40, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 40, "endColumn": 35}, {"ruleId": "922", "severity": 1, "message": "940", "line": 40, "column": 37, "nodeType": "924", "messageId": "925", "endLine": 40, "endColumn": 62}, {"ruleId": "922", "severity": 1, "message": "941", "line": 57, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 57, "endColumn": 22}, {"ruleId": "922", "severity": 1, "message": "942", "line": 58, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 58, "endColumn": 23}, {"ruleId": "922", "severity": 1, "message": "943", "line": 59, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 59, "endColumn": 26}, {"ruleId": "922", "severity": 1, "message": "944", "line": 60, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 60, "endColumn": 22}, {"ruleId": "922", "severity": 1, "message": "945", "line": 69, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 69, "endColumn": 29}, {"ruleId": "922", "severity": 1, "message": "946", "line": 1, "column": 8, "nodeType": "924", "messageId": "925", "endLine": 1, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "947", "line": 2, "column": 27, "nodeType": "924", "messageId": "925", "endLine": 2, "endColumn": 31}, {"ruleId": "922", "severity": 1, "message": "948", "line": 2, "column": 33, "nodeType": "924", "messageId": "925", "endLine": 2, "endColumn": 37}, {"ruleId": "922", "severity": 1, "message": "949", "line": 2, "column": 39, "nodeType": "924", "messageId": "925", "endLine": 2, "endColumn": 50}, {"ruleId": "922", "severity": 1, "message": "950", "line": 2, "column": 52, "nodeType": "924", "messageId": "925", "endLine": 2, "endColumn": 66}, {"ruleId": "922", "severity": 1, "message": "934", "line": 2, "column": 68, "nodeType": "924", "messageId": "925", "endLine": 2, "endColumn": 74}, {"ruleId": "922", "severity": 1, "message": "935", "line": 5, "column": 25, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 34}, {"ruleId": "922", "severity": 1, "message": "936", "line": 6, "column": 19, "nodeType": "924", "messageId": "925", "endLine": 6, "endColumn": 35}, {"ruleId": "922", "severity": 1, "message": "937", "line": 7, "column": 12, "nodeType": "924", "messageId": "925", "endLine": 7, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "938", "line": 8, "column": 18, "nodeType": "924", "messageId": "925", "endLine": 8, "endColumn": 28}, {"ruleId": "922", "severity": 1, "message": "951", "line": 43, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 43, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "948", "line": 8, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 8, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "949", "line": 9, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 9, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "923", "line": 10, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 10, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "947", "line": 11, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 11, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "952", "line": 12, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 12, "endColumn": 10}, {"ruleId": "922", "severity": 1, "message": "953", "line": 15, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 15, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "954", "line": 16, "column": 15, "nodeType": "924", "messageId": "925", "endLine": 16, "endColumn": 27}, {"ruleId": "922", "severity": 1, "message": "955", "line": 17, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 17, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "956", "line": 18, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 18, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "957", "line": 19, "column": 13, "nodeType": "924", "messageId": "925", "endLine": 19, "endColumn": 23}, {"ruleId": "922", "severity": 1, "message": "958", "line": 20, "column": 14, "nodeType": "924", "messageId": "925", "endLine": 20, "endColumn": 25}, {"ruleId": "922", "severity": 1, "message": "959", "line": 25, "column": 8, "nodeType": "924", "messageId": "925", "endLine": 25, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "960", "line": 28, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 28, "endColumn": 26}, {"ruleId": "922", "severity": 1, "message": "961", "line": 48, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 48, "endColumn": 22}, {"ruleId": "922", "severity": 1, "message": "962", "line": 53, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 53, "endColumn": 20}, {"ruleId": "922", "severity": 1, "message": "963", "line": 11, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 11, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "964", "line": 4, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 4, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 8}, {"ruleId": "922", "severity": 1, "message": "966", "line": 7, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 7, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "967", "line": 12, "column": 14, "nodeType": "924", "messageId": "925", "endLine": 12, "endColumn": 25}, {"ruleId": "922", "severity": 1, "message": "953", "line": 13, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 13, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "968", "line": 17, "column": 8, "nodeType": "924", "messageId": "925", "endLine": 17, "endColumn": 23}, {"ruleId": "922", "severity": 1, "message": "960", "line": 21, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 21, "endColumn": 26}, {"ruleId": "922", "severity": 1, "message": "969", "line": 26, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 26, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "948", "line": 8, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 8, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "949", "line": 9, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 9, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "966", "line": 11, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 11, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "970", "line": 14, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 14, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "971", "line": 26, "column": 8, "nodeType": "924", "messageId": "925", "endLine": 26, "endColumn": 16}, {"ruleId": "922", "severity": 1, "message": "972", "line": 30, "column": 15, "nodeType": "924", "messageId": "925", "endLine": 30, "endColumn": 27}, {"ruleId": "922", "severity": 1, "message": "973", "line": 32, "column": 14, "nodeType": "924", "messageId": "925", "endLine": 32, "endColumn": 25}, {"ruleId": "922", "severity": 1, "message": "974", "line": 33, "column": 15, "nodeType": "924", "messageId": "925", "endLine": 33, "endColumn": 27}, {"ruleId": "922", "severity": 1, "message": "975", "line": 34, "column": 15, "nodeType": "924", "messageId": "925", "endLine": 34, "endColumn": 27}, {"ruleId": "922", "severity": 1, "message": "976", "line": 35, "column": 27, "nodeType": "924", "messageId": "925", "endLine": 35, "endColumn": 51}, {"ruleId": "922", "severity": 1, "message": "977", "line": 41, "column": 15, "nodeType": "924", "messageId": "925", "endLine": 41, "endColumn": 27}, {"ruleId": "922", "severity": 1, "message": "978", "line": 50, "column": 8, "nodeType": "924", "messageId": "925", "endLine": 50, "endColumn": 16}, {"ruleId": "922", "severity": 1, "message": "960", "line": 60, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 60, "endColumn": 26}, {"ruleId": "922", "severity": 1, "message": "979", "line": 62, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 62, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "969", "line": 64, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 64, "endColumn": 22}, {"ruleId": "922", "severity": 1, "message": "980", "line": 258, "column": 19, "nodeType": "924", "messageId": "925", "endLine": 258, "endColumn": 29}, {"ruleId": "922", "severity": 1, "message": "981", "line": 266, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 266, "endColumn": 28}, {"ruleId": "922", "severity": 1, "message": "982", "line": 267, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 267, "endColumn": 23}, {"ruleId": "922", "severity": 1, "message": "983", "line": 267, "column": 25, "nodeType": "924", "messageId": "925", "endLine": 267, "endColumn": 41}, {"ruleId": "984", "severity": 1, "message": "985", "line": 627, "column": 6, "nodeType": "986", "endLine": 627, "endColumn": 15, "suggestions": "987"}, {"ruleId": "922", "severity": 1, "message": "988", "line": 672, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 672, "endColumn": 27}, {"ruleId": "922", "severity": 1, "message": "989", "line": 1, "column": 27, "nodeType": "924", "messageId": "925", "endLine": 1, "endColumn": 36}, {"ruleId": "922", "severity": 1, "message": "990", "line": 49, "column": 19, "nodeType": "924", "messageId": "925", "endLine": 49, "endColumn": 26}, {"ruleId": "922", "severity": 1, "message": "952", "line": 15, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 15, "endColumn": 10}, {"ruleId": "922", "severity": 1, "message": "967", "line": 39, "column": 14, "nodeType": "924", "messageId": "925", "endLine": 39, "endColumn": 25}, {"ruleId": "922", "severity": 1, "message": "991", "line": 43, "column": 16, "nodeType": "924", "messageId": "925", "endLine": 43, "endColumn": 29}, {"ruleId": "922", "severity": 1, "message": "964", "line": 4, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 4, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 8}, {"ruleId": "922", "severity": 1, "message": "969", "line": 26, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 26, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "992", "line": 48, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 48, "endColumn": 29}, {"ruleId": "922", "severity": 1, "message": "964", "line": 4, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 4, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "992", "line": 37, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 37, "endColumn": 29}, {"ruleId": "922", "severity": 1, "message": "964", "line": 4, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 4, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "992", "line": 52, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 52, "endColumn": 29}, {"ruleId": "922", "severity": 1, "message": "964", "line": 4, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 4, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 8}, {"ruleId": "922", "severity": 1, "message": "969", "line": 26, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 26, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "992", "line": 48, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 48, "endColumn": 29}, {"ruleId": "922", "severity": 1, "message": "964", "line": 4, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 4, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 8}, {"ruleId": "922", "severity": 1, "message": "969", "line": 26, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 26, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "992", "line": 48, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 48, "endColumn": 29}, {"ruleId": "922", "severity": 1, "message": "969", "line": 27, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 27, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 8}, {"ruleId": "922", "severity": 1, "message": "993", "line": 6, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 6, "endColumn": 9}, {"ruleId": "922", "severity": 1, "message": "953", "line": 14, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 14, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "960", "line": 23, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 23, "endColumn": 26}, {"ruleId": "922", "severity": 1, "message": "969", "line": 30, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 30, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "992", "line": 33, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 33, "endColumn": 29}, {"ruleId": "922", "severity": 1, "message": "994", "line": 38, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 38, "endColumn": 26}, {"ruleId": "922", "severity": 1, "message": "960", "line": 24, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 24, "endColumn": 26}, {"ruleId": "984", "severity": 1, "message": "995", "line": 53, "column": 6, "nodeType": "986", "endLine": 53, "endColumn": 18, "suggestions": "996"}, {"ruleId": "922", "severity": 1, "message": "997", "line": 1, "column": 8, "nodeType": "924", "messageId": "925", "endLine": 1, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "998", "line": 5, "column": 7, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "952", "line": 14, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 14, "endColumn": 10}, {"ruleId": "922", "severity": 1, "message": "999", "line": 28, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 28, "endColumn": 18}, {"ruleId": "922", "severity": 1, "message": "997", "line": 1, "column": 8, "nodeType": "924", "messageId": "925", "endLine": 1, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "998", "line": 5, "column": 7, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "948", "line": 8, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 8, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "949", "line": 9, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 9, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "923", "line": 10, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 10, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "1000", "line": 23, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 23, "endColumn": 15}, {"ruleId": "922", "severity": 1, "message": "1001", "line": 24, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 24, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "954", "line": 46, "column": 15, "nodeType": "924", "messageId": "925", "endLine": 46, "endColumn": 27}, {"ruleId": "922", "severity": 1, "message": "1002", "line": 47, "column": 12, "nodeType": "924", "messageId": "925", "endLine": 47, "endColumn": 21}, {"ruleId": "984", "severity": 1, "message": "1003", "line": 134, "column": 6, "nodeType": "986", "endLine": 134, "endColumn": 18, "suggestions": "1004"}, {"ruleId": "922", "severity": 1, "message": "948", "line": 8, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 8, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "949", "line": 9, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 9, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "923", "line": 10, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 10, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "1000", "line": 23, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 23, "endColumn": 15}, {"ruleId": "922", "severity": 1, "message": "1001", "line": 24, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 24, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "952", "line": 25, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 25, "endColumn": 10}, {"ruleId": "922", "severity": 1, "message": "966", "line": 29, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 29, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "956", "line": 39, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 39, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "954", "line": 43, "column": 15, "nodeType": "924", "messageId": "925", "endLine": 43, "endColumn": 27}, {"ruleId": "922", "severity": 1, "message": "1005", "line": 44, "column": 14, "nodeType": "924", "messageId": "925", "endLine": 44, "endColumn": 25}, {"ruleId": "922", "severity": 1, "message": "1006", "line": 50, "column": 69, "nodeType": "924", "messageId": "925", "endLine": 50, "endColumn": 76}, {"ruleId": "922", "severity": 1, "message": "1007", "line": 79, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 79, "endColumn": 26}, {"ruleId": "984", "severity": 1, "message": "1008", "line": 179, "column": 6, "nodeType": "986", "endLine": 179, "endColumn": 8, "suggestions": "1009"}, {"ruleId": "922", "severity": 1, "message": "1010", "line": 697, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 697, "endColumn": 26}, {"ruleId": "922", "severity": 1, "message": "1011", "line": 20, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 20, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "1012", "line": 21, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 21, "endColumn": 9}, {"ruleId": "922", "severity": 1, "message": "1013", "line": 22, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 22, "endColumn": 11}, {"ruleId": "922", "severity": 1, "message": "947", "line": 23, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 23, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "1014", "line": 26, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 26, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "1015", "line": 69, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 69, "endColumn": 22}, {"ruleId": "1016", "severity": 1, "message": "1017", "line": 445, "column": 9, "nodeType": "1018", "messageId": "1019", "endLine": 448, "endColumn": 10}, {"ruleId": "930", "severity": 1, "message": "931", "line": 260, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 264, "endColumn": 11}, {"ruleId": "930", "severity": 1, "message": "931", "line": 274, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 274, "endColumn": 70}, {"ruleId": "930", "severity": 1, "message": "931", "line": 278, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 278, "endColumn": 54}, {"ruleId": "930", "severity": 1, "message": "931", "line": 333, "column": 11, "nodeType": "932", "messageId": "933", "endLine": 338, "endColumn": 13}, {"ruleId": "930", "severity": 1, "message": "931", "line": 435, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 439, "endColumn": 11}, {"ruleId": "930", "severity": 1, "message": "931", "line": 451, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 451, "endColumn": 54}, {"ruleId": "930", "severity": 1, "message": "931", "line": 668, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 668, "endColumn": 163}, {"ruleId": "930", "severity": 1, "message": "931", "line": 677, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 677, "endColumn": 70}, {"ruleId": "930", "severity": 1, "message": "931", "line": 681, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 681, "endColumn": 54}, {"ruleId": "922", "severity": 1, "message": "1020", "line": 755, "column": 17, "nodeType": "924", "messageId": "925", "endLine": 755, "endColumn": 22}, {"ruleId": "930", "severity": 1, "message": "931", "line": 775, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 779, "endColumn": 11}, {"ruleId": "930", "severity": 1, "message": "931", "line": 794, "column": 11, "nodeType": "932", "messageId": "933", "endLine": 798, "endColumn": 13}, {"ruleId": "930", "severity": 1, "message": "931", "line": 801, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 804, "endColumn": 11}, {"ruleId": "930", "severity": 1, "message": "931", "line": 810, "column": 11, "nodeType": "932", "messageId": "933", "endLine": 814, "endColumn": 13}, {"ruleId": "930", "severity": 1, "message": "931", "line": 817, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 820, "endColumn": 11}, {"ruleId": "930", "severity": 1, "message": "931", "line": 885, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 889, "endColumn": 11}, {"ruleId": "1021", "severity": 1, "message": "1022", "line": 955, "column": 3, "nodeType": "1023", "messageId": "1024", "endLine": 955, "endColumn": 29}, {"ruleId": "1021", "severity": 1, "message": "1025", "line": 1143, "column": 3, "nodeType": "1023", "messageId": "1024", "endLine": 1143, "endColumn": 23}, {"ruleId": "1021", "severity": 1, "message": "1026", "line": 1238, "column": 3, "nodeType": "1023", "messageId": "1024", "endLine": 1238, "endColumn": 20}, {"ruleId": "930", "severity": 1, "message": "931", "line": 1287, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 1287, "endColumn": 163}, {"ruleId": "930", "severity": 1, "message": "931", "line": 1317, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 1317, "endColumn": 163}, {"ruleId": "930", "severity": 1, "message": "931", "line": 1370, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 1370, "endColumn": 163}, {"ruleId": "930", "severity": 1, "message": "931", "line": 1412, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 1412, "endColumn": 163}, {"ruleId": "922", "severity": 1, "message": "1027", "line": 6, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 6, "endColumn": 8}, {"ruleId": "922", "severity": 1, "message": "952", "line": 11, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 11, "endColumn": 10}, {"ruleId": "922", "severity": 1, "message": "964", "line": 2, "column": 15, "nodeType": "924", "messageId": "925", "endLine": 2, "endColumn": 25}, {"ruleId": "922", "severity": 1, "message": "993", "line": 2, "column": 64, "nodeType": "924", "messageId": "925", "endLine": 2, "endColumn": 70}, {"ruleId": "922", "severity": 1, "message": "975", "line": 4, "column": 15, "nodeType": "924", "messageId": "925", "endLine": 4, "endColumn": 27}, {"ruleId": "922", "severity": 1, "message": "1028", "line": 5, "column": 12, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "1029", "line": 6, "column": 17, "nodeType": "924", "messageId": "925", "endLine": 6, "endColumn": 26}, {"ruleId": "922", "severity": 1, "message": "977", "line": 7, "column": 15, "nodeType": "924", "messageId": "925", "endLine": 7, "endColumn": 27}, {"ruleId": "922", "severity": 1, "message": "1030", "line": 8, "column": 16, "nodeType": "924", "messageId": "925", "endLine": 8, "endColumn": 25}, {"ruleId": "922", "severity": 1, "message": "1031", "line": 121, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 121, "endColumn": 29}, {"ruleId": "922", "severity": 1, "message": "997", "line": 1, "column": 8, "nodeType": "924", "messageId": "925", "endLine": 1, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "998", "line": 5, "column": 7, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "1032", "line": 3, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 3, "endColumn": 11}, {"ruleId": "922", "severity": 1, "message": "1033", "line": 4, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 4, "endColumn": 6}, {"ruleId": "922", "severity": 1, "message": "1034", "line": 5, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "1035", "line": 6, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 6, "endColumn": 11}, {"ruleId": "922", "severity": 1, "message": "1036", "line": 7, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 7, "endColumn": 6}, {"ruleId": "922", "severity": 1, "message": "1037", "line": 12, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 12, "endColumn": 9}, {"ruleId": "922", "severity": 1, "message": "1038", "line": 36, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 36, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "1039", "line": 50, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 50, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "1040", "line": 64, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 64, "endColumn": 20}, {"ruleId": "922", "severity": 1, "message": "1041", "line": 88, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 88, "endColumn": 22}, {"ruleId": "922", "severity": 1, "message": "1042", "line": 104, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 104, "endColumn": 30}, {"ruleId": "922", "severity": 1, "message": "1043", "line": 3, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 3, "endColumn": 12}, {"ruleId": "922", "severity": 1, "message": "1035", "line": 3, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 3, "endColumn": 11}, {"ruleId": "922", "severity": 1, "message": "1036", "line": 4, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 4, "endColumn": 6}, {"ruleId": "922", "severity": 1, "message": "1044", "line": 5, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 8}, {"ruleId": "922", "severity": 1, "message": "1045", "line": 6, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 6, "endColumn": 8}, {"ruleId": "922", "severity": 1, "message": "1046", "line": 7, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 7, "endColumn": 16}, {"ruleId": "922", "severity": 1, "message": "1047", "line": 8, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 8, "endColumn": 10}, {"ruleId": "922", "severity": 1, "message": "1037", "line": 9, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 9, "endColumn": 9}, {"ruleId": "922", "severity": 1, "message": "1048", "line": 10, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 10, "endColumn": 22}, {"ruleId": "922", "severity": 1, "message": "1032", "line": 11, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 11, "endColumn": 11}, {"ruleId": "922", "severity": 1, "message": "1033", "line": 12, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 12, "endColumn": 6}, {"ruleId": "922", "severity": 1, "message": "1034", "line": 13, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 13, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "1049", "line": 14, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 14, "endColumn": 16}, {"ruleId": "922", "severity": 1, "message": "1050", "line": 15, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 15, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "1043", "line": 16, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 16, "endColumn": 12}, {"ruleId": "922", "severity": 1, "message": "1051", "line": 18, "column": 40, "nodeType": "924", "messageId": "925", "endLine": 18, "endColumn": 44}, {"ruleId": "922", "severity": 1, "message": "1052", "line": 47, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 47, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "1053", "line": 64, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 64, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "1054", "line": 71, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 71, "endColumn": 20}, {"ruleId": "922", "severity": 1, "message": "1041", "line": 79, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 79, "endColumn": 22}, {"ruleId": "922", "severity": 1, "message": "1042", "line": 95, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 95, "endColumn": 30}, {"ruleId": "922", "severity": 1, "message": "1055", "line": 299, "column": 27, "nodeType": "924", "messageId": "925", "endLine": 299, "endColumn": 37}, {"ruleId": "922", "severity": 1, "message": "1056", "line": 300, "column": 27, "nodeType": "924", "messageId": "925", "endLine": 300, "endColumn": 36}, {"ruleId": "922", "severity": 1, "message": "965", "line": 3, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 3, "endColumn": 8}, {"ruleId": "984", "severity": 1, "message": "1057", "line": 54, "column": 6, "nodeType": "986", "endLine": 54, "endColumn": 34, "suggestions": "1058"}, {"ruleId": "922", "severity": 1, "message": "1059", "line": 25, "column": 13, "nodeType": "924", "messageId": "925", "endLine": 25, "endColumn": 25}, {"ruleId": "922", "severity": 1, "message": "1060", "line": 33, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 33, "endColumn": 15}, {"ruleId": "922", "severity": 1, "message": "1061", "line": 34, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 34, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "1062", "line": 35, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 35, "endColumn": 22}, {"ruleId": "922", "severity": 1, "message": "1063", "line": 36, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 36, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "1064", "line": 37, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 37, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "1065", "line": 41, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 41, "endColumn": 20}, {"ruleId": "922", "severity": 1, "message": "1066", "line": 43, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 43, "endColumn": 34}, {"ruleId": "922", "severity": 1, "message": "1067", "line": 69, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 69, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "1068", "line": 69, "column": 19, "nodeType": "924", "messageId": "925", "endLine": 69, "endColumn": 29}, {"ruleId": "984", "severity": 1, "message": "1069", "line": 88, "column": 6, "nodeType": "986", "endLine": 88, "endColumn": 18, "suggestions": "1070"}, {"ruleId": "984", "severity": 1, "message": "1071", "line": 448, "column": 6, "nodeType": "986", "endLine": 448, "endColumn": 28, "suggestions": "1072"}, {"ruleId": "922", "severity": 1, "message": "1073", "line": 4, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 4, "endColumn": 12}, {"ruleId": "922", "severity": 1, "message": "1074", "line": 8, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 8, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "1075", "line": 9, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 9, "endColumn": 11}, {"ruleId": "922", "severity": 1, "message": "1076", "line": 10, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 10, "endColumn": 15}, {"ruleId": "922", "severity": 1, "message": "1077", "line": 12, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 12, "endColumn": 9}, {"ruleId": "922", "severity": 1, "message": "1078", "line": 13, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 13, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "1079", "line": 14, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 14, "endColumn": 16}, {"ruleId": "922", "severity": 1, "message": "1080", "line": 15, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 15, "endColumn": 16}, {"ruleId": "922", "severity": 1, "message": "1081", "line": 36, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 36, "endColumn": 30}, {"ruleId": "922", "severity": 1, "message": "1082", "line": 37, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 37, "endColumn": 20}, {"ruleId": "984", "severity": 1, "message": "1057", "line": 46, "column": 6, "nodeType": "986", "endLine": 46, "endColumn": 18, "suggestions": "1083"}, {"ruleId": "922", "severity": 1, "message": "1084", "line": 265, "column": 23, "nodeType": "924", "messageId": "925", "endLine": 265, "endColumn": 44}, {"ruleId": "922", "severity": 1, "message": "1085", "line": 266, "column": 23, "nodeType": "924", "messageId": "925", "endLine": 266, "endColumn": 42}, {"ruleId": "922", "severity": 1, "message": "1084", "line": 381, "column": 21, "nodeType": "924", "messageId": "925", "endLine": 381, "endColumn": 42}, {"ruleId": "922", "severity": 1, "message": "1085", "line": 382, "column": 21, "nodeType": "924", "messageId": "925", "endLine": 382, "endColumn": 40}, {"ruleId": "922", "severity": 1, "message": "1086", "line": 9, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 9, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "1011", "line": 10, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 10, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "1012", "line": 11, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 11, "endColumn": 9}, {"ruleId": "922", "severity": 1, "message": "1013", "line": 12, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 12, "endColumn": 11}, {"ruleId": "922", "severity": 1, "message": "1076", "line": 33, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 33, "endColumn": 15}, {"ruleId": "922", "severity": 1, "message": "1087", "line": 35, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 35, "endColumn": 26}, {"ruleId": "922", "severity": 1, "message": "1005", "line": 42, "column": 14, "nodeType": "924", "messageId": "925", "endLine": 42, "endColumn": 25}, {"ruleId": "922", "severity": 1, "message": "1060", "line": 52, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 52, "endColumn": 15}, {"ruleId": "922", "severity": 1, "message": "1061", "line": 53, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 53, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "1062", "line": 54, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 54, "endColumn": 22}, {"ruleId": "922", "severity": 1, "message": "1063", "line": 55, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 55, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "1064", "line": 56, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 56, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "1088", "line": 57, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 57, "endColumn": 15}, {"ruleId": "922", "severity": 1, "message": "1089", "line": 58, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 58, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "1090", "line": 59, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 59, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "979", "line": 72, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 72, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "1091", "line": 79, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 79, "endColumn": 23}, {"ruleId": "922", "severity": 1, "message": "1092", "line": 79, "column": 25, "nodeType": "924", "messageId": "925", "endLine": 79, "endColumn": 41}, {"ruleId": "922", "severity": 1, "message": "1093", "line": 80, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 80, "endColumn": 27}, {"ruleId": "922", "severity": 1, "message": "1094", "line": 85, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 85, "endColumn": 26}, {"ruleId": "984", "severity": 1, "message": "1057", "line": 105, "column": 6, "nodeType": "986", "endLine": 105, "endColumn": 18, "suggestions": "1095"}, {"ruleId": "984", "severity": 1, "message": "1096", "line": 112, "column": 6, "nodeType": "986", "endLine": 112, "endColumn": 20, "suggestions": "1097"}, {"ruleId": "984", "severity": 1, "message": "1098", "line": 127, "column": 6, "nodeType": "986", "endLine": 127, "endColumn": 34, "suggestions": "1099"}, {"ruleId": "922", "severity": 1, "message": "1100", "line": 283, "column": 13, "nodeType": "924", "messageId": "925", "endLine": 283, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "1014", "line": 17, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 17, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "1076", "line": 34, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 34, "endColumn": 15}, {"ruleId": "922", "severity": 1, "message": "1087", "line": 35, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 35, "endColumn": 26}, {"ruleId": "922", "severity": 1, "message": "1101", "line": 39, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 39, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "1060", "line": 51, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 51, "endColumn": 15}, {"ruleId": "922", "severity": 1, "message": "1061", "line": 52, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 52, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "1063", "line": 54, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 54, "endColumn": 21}, {"ruleId": "922", "severity": 1, "message": "1064", "line": 55, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 55, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "1066", "line": 62, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 62, "endColumn": 34}, {"ruleId": "922", "severity": 1, "message": "1102", "line": 105, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 105, "endColumn": 26}, {"ruleId": "922", "severity": 1, "message": "1103", "line": 105, "column": 28, "nodeType": "924", "messageId": "925", "endLine": 105, "endColumn": 47}, {"ruleId": "984", "severity": 1, "message": "1096", "line": 145, "column": 6, "nodeType": "986", "endLine": 145, "endColumn": 18, "suggestions": "1104"}, {"ruleId": "922", "severity": 1, "message": "1105", "line": 701, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 701, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "1106", "line": 1311, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 1311, "endColumn": 28}, {"ruleId": "922", "severity": 1, "message": "1107", "line": 1316, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 1316, "endColumn": 30}, {"ruleId": "922", "severity": 1, "message": "1108", "line": 1883, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 1883, "endColumn": 23}, {"ruleId": "922", "severity": 1, "message": "997", "line": 1, "column": 8, "nodeType": "924", "messageId": "925", "endLine": 1, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "998", "line": 5, "column": 7, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "1109", "line": 1, "column": 8, "nodeType": "924", "messageId": "925", "endLine": 1, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "997", "line": 1, "column": 8, "nodeType": "924", "messageId": "925", "endLine": 1, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "998", "line": 5, "column": 7, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "997", "line": 1, "column": 8, "nodeType": "924", "messageId": "925", "endLine": 1, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "998", "line": 5, "column": 7, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "1110", "line": 83, "column": 13, "nodeType": "924", "messageId": "925", "endLine": 83, "endColumn": 21}, {"ruleId": "930", "severity": 1, "message": "931", "line": 109, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 109, "endColumn": 163}, {"ruleId": "930", "severity": 1, "message": "931", "line": 123, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 123, "endColumn": 70}, {"ruleId": "930", "severity": 1, "message": "931", "line": 127, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 127, "endColumn": 54}, {"ruleId": "930", "severity": 1, "message": "931", "line": 212, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 212, "endColumn": 163}, {"ruleId": "930", "severity": 1, "message": "931", "line": 226, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 226, "endColumn": 70}, {"ruleId": "930", "severity": 1, "message": "931", "line": 230, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 230, "endColumn": 54}, {"ruleId": "930", "severity": 1, "message": "931", "line": 271, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 271, "endColumn": 163}, {"ruleId": "930", "severity": 1, "message": "931", "line": 280, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 280, "endColumn": 70}, {"ruleId": "930", "severity": 1, "message": "931", "line": 284, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 284, "endColumn": 54}, {"ruleId": "930", "severity": 1, "message": "931", "line": 320, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 320, "endColumn": 70}, {"ruleId": "930", "severity": 1, "message": "931", "line": 324, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 324, "endColumn": 54}, {"ruleId": "930", "severity": 1, "message": "931", "line": 360, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 360, "endColumn": 163}, {"ruleId": "930", "severity": 1, "message": "931", "line": 369, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 369, "endColumn": 70}, {"ruleId": "930", "severity": 1, "message": "931", "line": 373, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 373, "endColumn": 54}, {"ruleId": "930", "severity": 1, "message": "931", "line": 450, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 450, "endColumn": 163}, {"ruleId": "930", "severity": 1, "message": "931", "line": 459, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 459, "endColumn": 70}, {"ruleId": "930", "severity": 1, "message": "931", "line": 463, "column": 9, "nodeType": "932", "messageId": "933", "endLine": 463, "endColumn": 54}, {"ruleId": "922", "severity": 1, "message": "1111", "line": 12, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 12, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "955", "line": 27, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 27, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "971", "line": 30, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 30, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "1062", "line": 34, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 34, "endColumn": 29}, {"ruleId": "922", "severity": 1, "message": "1067", "line": 49, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 49, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "1068", "line": 49, "column": 19, "nodeType": "924", "messageId": "925", "endLine": 49, "endColumn": 29}, {"ruleId": "984", "severity": 1, "message": "1057", "line": 64, "column": 6, "nodeType": "986", "endLine": 64, "endColumn": 32, "suggestions": "1112"}, {"ruleId": "922", "severity": 1, "message": "1113", "line": 270, "column": 17, "nodeType": "924", "messageId": "925", "endLine": 270, "endColumn": 23}, {"ruleId": "922", "severity": 1, "message": "1114", "line": 17, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 17, "endColumn": 8}, {"ruleId": "922", "severity": 1, "message": "1013", "line": 16, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 16, "endColumn": 11}, {"ruleId": "922", "severity": 1, "message": "1012", "line": 17, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 17, "endColumn": 9}, {"ruleId": "922", "severity": 1, "message": "1011", "line": 19, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 19, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "971", "line": 14, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 14, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "1115", "line": 43, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 43, "endColumn": 26}, {"ruleId": "922", "severity": 1, "message": "966", "line": 12, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 12, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "1116", "line": 33, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 33, "endColumn": 29}, {"ruleId": "922", "severity": 1, "message": "1117", "line": 3, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 3, "endColumn": 6}, {"ruleId": "922", "severity": 1, "message": "952", "line": 9, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 9, "endColumn": 10}, {"ruleId": "922", "severity": 1, "message": "1118", "line": 20, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 20, "endColumn": 19}, {"ruleId": "984", "severity": 1, "message": "1119", "line": 136, "column": 6, "nodeType": "986", "endLine": 136, "endColumn": 18, "suggestions": "1120"}, {"ruleId": "984", "severity": 1, "message": "1121", "line": 141, "column": 6, "nodeType": "986", "endLine": 141, "endColumn": 52, "suggestions": "1122"}, {"ruleId": "984", "severity": 1, "message": "1123", "line": 146, "column": 6, "nodeType": "986", "endLine": 146, "endColumn": 62, "suggestions": "1124"}, {"ruleId": "984", "severity": 1, "message": "1125", "line": 151, "column": 6, "nodeType": "986", "endLine": 151, "endColumn": 28, "suggestions": "1126"}, {"ruleId": "984", "severity": 1, "message": "1127", "line": 160, "column": 6, "nodeType": "986", "endLine": 160, "endColumn": 39, "suggestions": "1128"}, {"ruleId": "984", "severity": 1, "message": "1129", "line": 68, "column": 6, "nodeType": "986", "endLine": 68, "endColumn": 18, "suggestions": "1130"}, {"ruleId": "922", "severity": 1, "message": "948", "line": 8, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 8, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "949", "line": 9, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 9, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "993", "line": 10, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 10, "endColumn": 9}, {"ruleId": "922", "severity": 1, "message": "1075", "line": 12, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 12, "endColumn": 11}, {"ruleId": "922", "severity": 1, "message": "1131", "line": 21, "column": 17, "nodeType": "924", "messageId": "925", "endLine": 21, "endColumn": 31}, {"ruleId": "922", "severity": 1, "message": "938", "line": 24, "column": 17, "nodeType": "924", "messageId": "925", "endLine": 24, "endColumn": 27}, {"ruleId": "984", "severity": 1, "message": "1132", "line": 180, "column": 6, "nodeType": "986", "endLine": 180, "endColumn": 25, "suggestions": "1133"}, {"ruleId": "926", "severity": 1, "message": "1134", "line": 243, "column": 15, "nodeType": "928", "messageId": "929", "endLine": 248, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "965", "line": 5, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 8}, {"ruleId": "922", "severity": 1, "message": "1001", "line": 15, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 15, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "952", "line": 16, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 16, "endColumn": 10}, {"ruleId": "922", "severity": 1, "message": "956", "line": 28, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 28, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "957", "line": 29, "column": 13, "nodeType": "924", "messageId": "925", "endLine": 29, "endColumn": 23}, {"ruleId": "922", "severity": 1, "message": "1135", "line": 39, "column": 34, "nodeType": "924", "messageId": "925", "endLine": 39, "endColumn": 59}, {"ruleId": "922", "severity": 1, "message": "1067", "line": 41, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 41, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "989", "line": 1, "column": 27, "nodeType": "924", "messageId": "925", "endLine": 1, "endColumn": 36}, {"ruleId": "922", "severity": 1, "message": "948", "line": 10, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 10, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "949", "line": 11, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 11, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "964", "line": 12, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 12, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "1114", "line": 27, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 27, "endColumn": 8}, {"ruleId": "922", "severity": 1, "message": "955", "line": 30, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 30, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "1136", "line": 33, "column": 17, "nodeType": "924", "messageId": "925", "endLine": 33, "endColumn": 25}, {"ruleId": "922", "severity": 1, "message": "938", "line": 34, "column": 17, "nodeType": "924", "messageId": "925", "endLine": 34, "endColumn": 27}, {"ruleId": "922", "severity": 1, "message": "967", "line": 35, "column": 14, "nodeType": "924", "messageId": "925", "endLine": 35, "endColumn": 25}, {"ruleId": "922", "severity": 1, "message": "948", "line": 10, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 10, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "949", "line": 11, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 11, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "1114", "line": 27, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 27, "endColumn": 8}, {"ruleId": "922", "severity": 1, "message": "1137", "line": 28, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 28, "endColumn": 12}, {"ruleId": "922", "severity": 1, "message": "1138", "line": 29, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 29, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "1139", "line": 30, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 30, "endColumn": 19}, {"ruleId": "922", "severity": 1, "message": "955", "line": 34, "column": 10, "nodeType": "924", "messageId": "925", "endLine": 34, "endColumn": 17}, {"ruleId": "922", "severity": 1, "message": "1140", "line": 37, "column": 17, "nodeType": "924", "messageId": "925", "endLine": 37, "endColumn": 31}, {"ruleId": "984", "severity": 1, "message": "1141", "line": 98, "column": 6, "nodeType": "986", "endLine": 98, "endColumn": 24, "suggestions": "1142"}, {"ruleId": "922", "severity": 1, "message": "948", "line": 4, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 4, "endColumn": 7}, {"ruleId": "922", "severity": 1, "message": "949", "line": 5, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 5, "endColumn": 14}, {"ruleId": "984", "severity": 1, "message": "1143", "line": 66, "column": 6, "nodeType": "986", "endLine": 66, "endColumn": 25, "suggestions": "1144"}, {"ruleId": "922", "severity": 1, "message": "1145", "line": 196, "column": 9, "nodeType": "924", "messageId": "925", "endLine": 196, "endColumn": 27}, {"ruleId": "922", "severity": 1, "message": "1146", "line": 233, "column": 11, "nodeType": "924", "messageId": "925", "endLine": 233, "endColumn": 24}, {"ruleId": "984", "severity": 1, "message": "1147", "line": 389, "column": 6, "nodeType": "986", "endLine": 389, "endColumn": 58, "suggestions": "1148"}, {"ruleId": "922", "severity": 1, "message": "1086", "line": 15, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 15, "endColumn": 14}, {"ruleId": "922", "severity": 1, "message": "1011", "line": 16, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 16, "endColumn": 13}, {"ruleId": "922", "severity": 1, "message": "1012", "line": 17, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 17, "endColumn": 9}, {"ruleId": "922", "severity": 1, "message": "1013", "line": 18, "column": 3, "nodeType": "924", "messageId": "925", "endLine": 18, "endColumn": 11}, "no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "react/jsx-pascal-case", "Imported JSX component CertificazioneCEI64_8Page must be in PascalCase or SCREAMING_SNAKE_CASE", "JSXOpeningElement", "usePascalOrSnakeCase", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'Avatar' is defined but never used.", "'AdminIcon' is defined but never used.", "'ConstructionIcon' is defined but never used.", "'CableIcon' is defined but never used.", "'ReportIcon' is defined but never used.", "'setOpenEliminaCavoDialog' is assigned a value but never used.", "'setOpenModificaCavoDialog' is assigned a value but never used.", "'homeAnchorEl' is assigned a value but never used.", "'adminAnchorEl' is assigned a value but never used.", "'cantieriAnchorEl' is assigned a value but never used.", "'caviAnchorEl' is assigned a value but never used.", "'selectedCantiereName' is assigned a value but never used.", "'React' is defined but never used.", "'Grid' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardActionArea' is defined but never used.", "'navigateTo' is assigned a value but never used.", "'Divider' is defined but never used.", "'HomeIcon' is defined but never used.", "'ViewListIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'EditIcon' is defined but never used.", "'DeleteIcon' is defined but never used.", "'HistoryIcon' is defined but never used.", "'ParcoCavi' is defined but never used.", "'isImpersonating' is assigned a value but never used.", "'handleSuccess' is assigned a value but never used.", "'handleError' is assigned a value but never used.", "'lastCheck' is assigned a value but never used.", "'Typography' is defined but never used.", "'Paper' is defined but never used.", "'IconButton' is defined but never used.", "'RefreshIcon' is defined but never used.", "'AdminHomeButton' is defined but never used.", "'cantiereName' is assigned a value but never used.", "'LinearProgress' is defined but never used.", "'InfoIcon' is defined but never used.", "'ScheduleIcon' is defined but never used.", "'LinkOffIcon' is defined but never used.", "'TimelineIcon' is defined but never used.", "'CheckBoxIcon' is defined but never used.", "'CheckBoxOutlineBlankIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'CavoForm' is defined but never used.", "'navigate' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'statiInstallazione' is assigned a value but never used.", "'tipologieCavi' is assigned a value but never used.", "'setTipologieCavi' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'caviAttivi', 'caviSpare', 'error', and 'user'. Either include them or remove the dependency array.", "ArrayExpression", ["1149"], "'getAllSelectedCavi' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useAuth' is assigned a value but never used.", "'InventoryIcon' is defined but never used.", "'handleBackToCantieri' is assigned a value but never used.", "'Button' is defined but never used.", "'handleBackToAdmin' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'selectCantiere'. Either include it or remove the dependency array.", ["1150"], "'axios' is defined but never used.", "'API_URL' is assigned a value but never used.", "'filePath' is assigned a value but never used.", "'ListItemIcon' is defined but never used.", "'ListItemButton' is defined but never used.", "'BuildIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCertificazioni'. Either include it or remove the dependency array.", ["1151"], "'WarningIcon' is defined but never used.", "'isEmpty' is defined but never used.", "'isFirstInsertion' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleOptionSelect', 'initialOption', and 'loadBobine'. Either include them or remove the dependency array.", ["1152"], "'renderBobineCards' is assigned a value but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormHelperText' is defined but never used.", "'formWarnings' is assigned a value but never used.", "no-unreachable", "Unreachable code.", "IfStatement", "unreachableCode", "'token' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'updateCavoForCompatibility'.", "ObjectExpression", "unexpected", "Duplicate key 'getRevisioneCorrente'.", "Duplicate key 'getCaviInstallati'.", "'Stack' is defined but never used.", "'ClearIcon' is defined but never used.", "'RulerIcon' is defined but never used.", "'StartIcon' is defined but never used.", "'handleClearSelection' is assigned a value but never used.", "'PieChart' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'Legend' is defined but never used.", "'progressData' is assigned a value but never used.", "'caviData' is assigned a value but never used.", "'metricsData' is assigned a value but never used.", "'CustomTooltip' is assigned a value but never used.", "'renderCustomizedLabel' is assigned a value but never used.", "'LineChart' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'Tooltip' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'ComposedChart' is defined but never used.", "'Line' is defined but never used.", "'Chip' is defined but never used.", "'bobineData' is assigned a value but never used.", "'totaliData' is assigned a value but never used.", "'analisiData' is assigned a value but never used.", "'isCompleto' is assigned a value but never used.", "'isInCorso' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadCavi'. Either include it or remove the dependency array.", ["1153"], "'DownloadIcon' is defined but never used.", "'CABLE_STATES' is defined but never used.", "'REEL_STATES' is defined but never used.", "'determineCableState' is defined but never used.", "'determineReelState' is defined but never used.", "'canModifyCable' is defined but never used.", "'getReelStateColor' is defined but never used.", "'redirectToVisualizzaCavi' is defined but never used.", "'loading' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadBobine' and 'loadCavi'. Either include them or remove the dependency array.", ["1154"], "React Hook useEffect has a missing dependency: 'filterCompatibleBobine'. Either include it or remove the dependency array.", ["1155"], "'TextField' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'internalSelectedCavo' is assigned a value but never used.", "'openDialog' is assigned a value but never used.", ["1156"], "'latoPartenzaCollegato' is assigned a value but never used.", "'latoArrivoCollegato' is assigned a value but never used.", "'FormControl' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'isCableSpare' is defined but never used.", "'isCableInstalled' is defined but never used.", "'getCableStateColor' is defined but never used.", "'searchResults' is assigned a value but never used.", "'setSearchResults' is assigned a value but never used.", "'showSearchResults' is assigned a value but never used.", "'compatibleBobine' is assigned a value but never used.", ["1157"], "React Hook useEffect has a missing dependency: 'loadBobine'. Either include it or remove the dependency array.", ["1158"], "React Hook useEffect has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1159"], "'bobina' is assigned a value but never used.", "'SaveIcon' is defined but never used.", "'incompatibleReel' is assigned a value but never used.", "'setIncompatibleReel' is assigned a value but never used.", ["1160"], "'handleBack' is assigned a value but never used.", "'buildFullBobinaId' is assigned a value but never used.", "'hasSufficientMeters' is assigned a value but never used.", "'getStepContent' is assigned a value but never used.", "'config' is defined but never used.", "'sentData' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", ["1161"], "'result' is assigned a value but never used.", "'Alert' is defined but never used.", "'filteredCantieri' is assigned a value but never used.", "'currentHoldDuration' is assigned a value but never used.", "'Box' is defined but never used.", "'CircularProgress' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["1162"], "React Hook useEffect has a missing dependency: 'filterCavi'. Either include it or remove the dependency array.", ["1163"], "React Hook useEffect has a missing dependency: 'filterCertificazioni'. Either include it or remove the dependency array.", ["1164"], "React Hook useEffect has a missing dependency: 'calculateStatistics'. Either include it or remove the dependency array.", ["1165"], "React Hook useEffect has missing dependencies: 'filterCavi' and 'filterCertificazioni'. Either include them or remove the dependency array.", ["1166"], "React Hook useEffect has missing dependencies: 'loadComande' and 'loadStatistiche'. Either include them or remove the dependency array.", ["1167"], "'AssignmentIcon' is defined but never used.", "React Hook React.useEffect has a missing dependency: 'getInitialAction'. Either include it or remove the dependency array.", ["1168"], "Imported JSX component CertificazioneCEI64_8 must be in PascalCase or SCREAMING_SNAKE_CASE", "'setSelectedCertificazione' is assigned a value but never used.", "'ViewIcon' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadProve'. Either include it or remove the dependency array.", ["1169"], "React Hook useEffect has a missing dependency: 'loadCaviDisponibili'. Either include it or remove the dependency array.", ["1170"], "'matchesNumericTerm' is assigned a value but never used.", "'isNumericTerm' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'cavoMatchesTerm'. Either include it or remove the dependency array.", ["1171"], {"desc": "1172", "fix": "1173"}, {"desc": "1174", "fix": "1175"}, {"desc": "1176", "fix": "1177"}, {"desc": "1178", "fix": "1179"}, {"desc": "1180", "fix": "1181"}, {"desc": "1182", "fix": "1183"}, {"desc": "1184", "fix": "1185"}, {"desc": "1186", "fix": "1187"}, {"desc": "1186", "fix": "1188"}, {"desc": "1189", "fix": "1190"}, {"desc": "1191", "fix": "1192"}, {"desc": "1193", "fix": "1194"}, {"desc": "1195", "fix": "1196"}, {"desc": "1197", "fix": "1198"}, {"desc": "1199", "fix": "1200"}, {"desc": "1201", "fix": "1202"}, {"desc": "1203", "fix": "1204"}, {"desc": "1205", "fix": "1206"}, {"desc": "1207", "fix": "1208"}, {"desc": "1209", "fix": "1210"}, {"desc": "1211", "fix": "1212"}, {"desc": "1213", "fix": "1214"}, {"desc": "1215", "fix": "1216"}, "Update the dependencies array to be: [caviAttivi, caviSpare, error, filters, user]", {"range": "1217", "text": "1218"}, "Update the dependencies array to be: [cantiereId, selectCantiere]", {"range": "1219", "text": "1220"}, "Update the dependencies array to be: [cantiereId, loadCertificazioni]", {"range": "1221", "text": "1222"}, "Update the dependencies array to be: [handleOptionSelect, initialOption, loadBobine]", {"range": "1223", "text": "1224"}, "Update the dependencies array to be: [certificazione, cantiereId, loadCavi]", {"range": "1225", "text": "1226"}, "Update the dependencies array to be: [cantiereId, loadBobine, loadCavi]", {"range": "1227", "text": "1228"}, "Update the dependencies array to be: [selectedCavo, bobine, filterCompatibleBobine]", {"range": "1229", "text": "1230"}, "Update the dependencies array to be: [cantiereId, loadCavi]", {"range": "1231", "text": "1232"}, {"range": "1233", "text": "1232"}, "Update the dependencies array to be: [loadBobine, selectedCavo]", {"range": "1234", "text": "1235"}, "Update the dependencies array to be: [cavoId, cavi, selectedCavo, onError]", {"range": "1236", "text": "1237"}, "Update the dependencies array to be: [cantiereId, loadBobine]", {"range": "1238", "text": "1239"}, "Update the dependencies array to be: [open, bobina, cantiereId, loadCavi]", {"range": "1240", "text": "1241"}, "Update the dependencies array to be: [cantiereId, loadInitialData]", {"range": "1242", "text": "1243"}, "Update the dependencies array to be: [cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", {"range": "1244", "text": "1245"}, "Update the dependencies array to be: [certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", {"range": "1246", "text": "1247"}, "Update the dependencies array to be: [calculateStatistics, cavi, certificazioni]", {"range": "1248", "text": "1249"}, "Update the dependencies array to be: [activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", {"range": "1250", "text": "1251"}, "Update the dependencies array to be: [cantiereId, loadComande, loadStatistiche]", {"range": "1252", "text": "1253"}, "Update the dependencies array to be: [getInitialAction, location.pathname]", {"range": "1254", "text": "1255"}, "Update the dependencies array to be: [certificazioneId, loadProve]", {"range": "1256", "text": "1257"}, "Update the dependencies array to be: [loadCaviDisponibili, open, tipoComanda]", {"range": "1258", "text": "1259"}, "Update the dependencies array to be: [searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]", {"range": "1260", "text": "1261"}, [25085, 25094], "[caviAttivi, caviSpare, error, filters, user]", [1559, 1571], "[cantiereId, selectCantiere]", [3538, 3550], "[cantiereId, loadCertificazioni]", [6583, 6585], "[handleOptionSelect, initialOption, loadBobine]", [1578, 1606], "[certificazione, cantiereId, loadCavi]", [2572, 2584], "[cantiereId, loadBobine, loadCavi]", [14450, 14472], "[selectedCavo, bobine, filterCompatibleBobine]", [1077, 1089], "[cantiereId, loadCavi]", [3142, 3154], [3288, 3302], "[load<PERSON><PERSON><PERSON>, selected<PERSON>avo]", [3868, 3896], "[cavoId, cavi, selected<PERSON>av<PERSON>, onError]", [4325, 4337], "[cantiereId, loadBobine]", [1912, 1938], "[open, bobina, cantiereId, loadCavi]", [3608, 3620], "[cantiereId, loadInitialData]", [3705, 3751], "[cavi, searchTerm, filters, sortBy, sortOrder, filterCavi]", [3835, 3891], "[certificazioni, searchTerm, filters, sortBy, sortOrder, filterCertificazioni]", [3997, 4019], "[calculateStatistics, cavi, certificazioni]", [4241, 4274], "[activeTab, cavi, certificazioni, filterCavi, filterCertificazioni]", [1648, 1660], "[cantiereId, loadComande, loadStatistiche]", [4982, 5001], "[getInitialAction, location.pathname]", [2516, 2534], "[certificazioneId, loadProve]", [1523, 1542], "[loadCaviDisponibili, open, tipoComanda]", [11274, 11326], "[searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm]"]